# Development Guide

## 🔧 Development Mode (No Rebuilding Required!)

For faster development without rebuilding every time:

### Option 1: Watch Mode (Recommended)
```bash
npm run build:watch
```
This will automatically rebuild when you make changes to the code.

### Option 2: Development Build
```bash
npm run build:dev
```
This creates a development build with source maps for easier debugging.

## 🐛 Debugging with Logs

The extension now has comprehensive logging. To see the logs:

### 1. Background Script Logs
- Open Chrome DevTools
- Go to `chrome://extensions/`
- Find "Prajasakti Epaper Downloader"
- Click "service worker" link
- Check the console for background script logs

### 2. Popup Logs
- Right-click on the extension icon
- Select "Inspect popup"
- Check the console for popup logs

### 3. Content Script Logs
- Go to `epaper.prajasakti.com`
- Open DevTools (F12)
- Check the console for content script logs

## 🍪 Cookie Debugging

Look for these log messages:

```
🍪 Starting cookie extraction...
🍪 getCookies: Requesting cookies for URL: https://epaper.prajasakti.com/
📨 Sending message to background: {action: "getCookies", url: "..."}
🔧 Background: Received message: {action: "getCookies", url: "..."}
🍪 Background: Getting cookies for URL: https://epaper.prajasakti.com/
🍪 getCookiesForSite: Fetching cookies for: https://epaper.prajasakti.com/
🍪 getCookiesForSite: Found X cookies
🍪 getCookiesForSite: Cookie details: [...]
🍪 Background: Successfully got cookies, length: XXX
🍪 getCookies: Success, cookies length: XXX
🍪 Extracted cookies: [first 100 chars]...
```

## 🚨 Common Issues & Solutions

### Issue: "No cookies available"
**Check:**
1. Are you on `epaper.prajasakti.com`?
2. Do you see cookie extraction logs?
3. Are cookies actually being set by the website?

**Debug:**
```javascript
// In browser console on epaper.prajasakti.com
document.cookie
```

### Issue: Extension not loading
**Check:**
1. Manifest.json syntax (no trailing commas)
2. All required files are in dist folder
3. Chrome extension permissions

### Issue: Background script not working
**Check:**
1. Service worker is active in chrome://extensions/
2. No errors in service worker console
3. Manifest permissions are correct

## 🔄 Development Workflow

1. **Make changes** to your code
2. **If using watch mode**: Changes auto-rebuild
3. **If not using watch mode**: Run `npm run build`
4. **Reload extension** in Chrome (click reload button in chrome://extensions/)
5. **Test** your changes
6. **Check logs** in DevTools if issues occur

## 📝 Log Symbols Guide

- 🍪 Cookie-related operations
- 📰 Epaper data operations
- 🌐 Network/URL operations
- 📨 Message passing
- 🔧 Background script operations
- ✅ Success operations
- ❌ Error operations
- ⚠️ Warning operations

## 🎯 Quick Debug Commands

### Check if extension is loaded:
```javascript
chrome.runtime.getManifest()
```

### Check current tab URL:
```javascript
chrome.tabs.query({active: true, currentWindow: true}, (tabs) => console.log(tabs[0].url))
```

### Check available cookies:
```javascript
chrome.cookies.getAll({url: "https://epaper.prajasakti.com/"}, (cookies) => console.log(cookies))
```

## 🚀 Production Build

When ready for production:
```bash
npm run build
```

This creates an optimized build without source maps or development features.
