// Types for Prajasakti Epaper Downloader

export interface Edition {
  id: string;
  name: string;
  url: string;
}

export interface EpaperData {
  date: string;
  editions: Edition[];
}

export interface DownloadRequest {
  url: string;
  filename: string;
  cookies: string;
}

export interface PageInfo {
  url: string;
  date: string | null;
  edition: string | null;
  pageNo: string | null;
  title: string;
}

export interface ChromeMessage {
  action: string;
  data?: any;
  url?: string;
  cookies?: string;
}

export interface ChromeResponse {
  success: boolean;
  data?: any;
  cookies?: string;
  error?: string;
  result?: string;
  pdfLink?: string;
  pageInfo?: PageInfo;
}
