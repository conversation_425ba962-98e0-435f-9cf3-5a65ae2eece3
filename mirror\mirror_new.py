import requests
import time
from PIL import Image
from io import BytesIO
import os
import threading
import subprocess

#date format:- 01_01_2022

def createFolder(folder):
	if folder not in os.listdir():
		os.mkdir(folder)

headers={
	"authority": "asset.harnscloud.com",
	"method": "GET",
	"path": "/PublicationData/Mirror/vkbgmr/2022/01/01/Page/01_01_2022_025_vkbgmr.jpg",
	"scheme": "https",
	"accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8",
	"accept-encoding": "gzip, deflate, br",
	"accept-language": "en-US,en;q=0.9",
	"origin": "https://epaper.timesgroup.com",
	"referer": "https://epaper.timesgroup.com/",
	'sec-ch-ua': '" Not A;Brand";v="99", "Chromium";v="96", "Google Chrome";v="96"',
	"sec-ch-ua-mobile": "?0",
	'sec-ch-ua-platform': '"Windows"',
	"sec-fetch-dest": "image",
	"sec-fetch-mode": "cors",
	"sec-fetch-site": "cross-site",
	"user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36",
}

class Mirror:
	def __init__(self,eid,name,folder,date=time.strftime("%d_%m_%Y")):
		self.eid=eid[0]
		self.id=eid[1]
		self.name=name
		self.folder=folder
		self.date=date
		self.quality=60
		self.dateList=date.split("_")

	def doDownload(self):
		imgs=[]
		for no in range(1,31):
			if no<10:
				n="0"+str(no)
			else:
				n=str(no)
			date=self.dateList
			eid=self.eid.replace("$date",self.date).replace("$no",n)
			url="https://asset.harnscloud.com/PublicationData/Mirror/"+self.id+"/"+date[2]+"/"+date[1]+"/"+date[0]+"/Page/"+eid+".jpg"
			try:
				imgs.append(Image.open(BytesIO(requests.get(url,headers=headers).content)))
			except Exception:
				pass
		return imgs

	def downloadPapers(self):
		try:
			print("Downloading started for",self.name)
			imgs=self.doDownload()
			img=imgs[0]
			imgs.pop(0)
			img.save(self.folder+"/"+self.name+'.pdf',save_all=True,optimize=True,quality=int(self.quality),append_images=imgs)
			print("Completed download, saved as:",self.folder+"/"+self.name+'.pdf')
		except Exception as e:
			print("Failed to download for",self.name)
			print(str(e))

folder="Mirror"+time.strftime("%d%m%Y")
createFolder(folder)

def AuthDat():
	name=input("Enter Name:    ")
	password=input("Enter Password:    ")
	data=requests.get("https://garudaauthservices.azurewebsites.net/api/validate?name="+name+"&password="+password+"&newspaper=mirror").json()
	if (data["status"]!="Success"):
		print(data["data"])
		exit()
	return data["data"]

def authUser():
	global name
	name=input("Enter User Name :")
	users=["datta","santhi","dwaraka"]
	if name not in users:
		print(name,"have no access for this program")
		input()
		exit()
	link="https://temp-21dd4-default-rtdb.firebaseio.com/clients/uuid/"+name+"/.json"
	res=requests.get(link).text[1:-1]
	uuid=subprocess.check_output('wmic csproduct get UUID').decode("utf-8").split("\n")[1].strip()
	if res!=uuid:
		method = "sendMessage"
		token = "1271352791:AAF2DSjJcLsE1BJpoh9CVuzKKlnaWSqfBss"
		requests.post(
		    url='https://api.telegram.org/bot{0}/{1}'.format(token, method),
		    data={'chat_id': 512930889, 'text': name+" : "+uuid+" new device registration"}
		)
		print("User doesn't register for this device, Althrough this device registration request will be sent")
		input()
		exit()
	return True

authUser()
papers={"Bangalore":["$date_0$no_vkbgmr","vkbgmr"],"Mumbai":["$date_0$no_vkmmir","vkmmir"],
		    "Pune":["$date_1$no_lxpmir","lxpmir"],"Ahmedabad":["$date_1$no_stplamir","stplamir"]}

for i in papers:
	if i+".pdf" not in os.listdir(folder):
		threading.Thread(target=Mirror(papers[i],i,folder).downloadPapers,args=()).start()
	else:
		print(i+".pdf is already present")