import requests
'''
for i in range(51000,52000):
	_id=str(i)	
	try:
		json_link="https://ereader.ulektz.com/reader/pdf/pdf_encrypt/decrypt.php?next_page=1&file_name=pass_"+_id+".epub&book_id="+_id
		data=session.get(json_link).json()
		print(i)
	except Exception:
		pass'''

import requests
'''
for i in range(51000,52000):
	_id=str(i)	
	try:
		json_link="https://ereader.ulektz.com/reader/pdf/pdf_encrypt/decrypt.php?next_page=1&file_name=pass_"+_id+".epub&book_id="+_id
		data=session.get(json_link).json()
		print(i)
	except Exception:
		pass'''

import requests
import threading
from requests.packages.urllib3.exceptions import InsecureRequestWarning

requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

session=requests.session()
checkbox={}
def check_site(no):
	global checkbox
	_id=str(no)
	try:
		res=session.get("https://ereader.ulektz.com/reader/pdf/pdf_encrypt/decrypt.php?next_page=1&file_name=pass_"+_id+".epub&book_id="+_id,verify=False)
		data=res.json()
		checkbox[no]=data['total_pages']
	except Exception:
		checkbox[no]=False

threads=[]
thread_speed=200
for i in range(48000,52000):
	k=threading.Thread(target=check_site,args=(i,))
	threads.append(k)
	k.start()
	if len(threads)==thread_speed:
		no=i-thread_speed+1
		for k in threads:
			k.join()
			if checkbox[no]!=False:
				print(no,"with",checkbox[no],"pages")
			no+=1
		threads=[]