import requests
from PIL import Image
from io import BytesIO
import os
import threading
from bs4 import BeautifulSoup
import time
import json
import gc
import subprocess

session=requests.session()
class readwhere:
	def __init__(self,url,folder,name):
		self.paperJson = session.get(url).json()
		self.folder=folder
		try:
			os.mkdir(folder)
		except Exception:
			pass
		self.name=name
		self.dim=[int(self.paperJson["1"]["levels"]["level1"]["width"]),int(self.paperJson["1"]["levels"]["level1"]["height"])]
		self.emptyImage=[]
		for i in self.paperJson:
			self.emptyImage.append(None)

	def formCompleteImg(self,url):
		url1=url
		url2=url[:-3]+'png'
		img = Image.open(BytesIO(session.get(url2).content)).convert("RGBA")
		background = Image.open(BytesIO(session.get(url1).content))
		background.paste(img, (0, 0), img)
		return background

	def makeEmptyPage(self):
		return Image.new('RGB',self.dim,'white')

	def addImgToEmpty(self,data,no):
		if (self.emptyImage[no-1]==None):
			self.emptyImage[no-1]=self.makeEmptyPage()
		self.emptyImage[no-1].resize((int(data["width"]),int(data["height"])))
		if (data['url'][-3:]=='png'):
			return
		else:
			self.emptyImage[no-1].paste(self.formCompleteImg(data["url"]),(int(data["tx"]),int(data["ty"])))

	def formPage(self,Paperdata,no):
		for i in Paperdata:
			self.addImgToEmpty(i,no)
		self.emptyImage[no-1].convert("RGB")

	def DownloadPaper(self):
		arr=[]
		for i in self.paperJson:
			t=threading.Thread(target=self.formPage,args=(self.paperJson[i]["levels"]["level1"]["chunks"],int(i)))
			arr.append(t)
			t.start()
		no=1
		for i in arr:
			print(no)
			no+=1
			i.join()
		img=self.emptyImage[0]
		self.emptyImage.pop(0)
		img.save(self.folder+'/'+self.name+'.pdf',save_all=True, append_images=self.emptyImage)

def getTodayId(_id):
	res=session.get('https://epaper.dishadaily.com/t/'+str(_id))
	soup=BeautifulSoup(res.text,"lxml")
	return soup.find("a",{"data-issue":_id})["href"].split("/")[-1]

def getallids():
	res=requests.get(baselink)
	soup=BeautifulSoup(res.text,"lxml")

	arr={"t":{},"r":{}}
	for i in soup.findAll("a"):
		try:
			link=i["href"].split("/")
			if (link[3]=="t"):
				arr["t"][link[4]]=i.text.strip().split("\n")[0].replace("/","-").replace(" ","")
			if (link[3]=="r"):
				arr["r"][link[4]]=i.text.strip().split("\n")[0].replace("/","-").replace(" ","")
		except Exception:
			pass
	return arr
#print(arr,len(arr["t"]),len(arr["r"]))

text="disha$https://epaper.dishadaily.com/"
for j in text.split("\n"):
	j=j.split("$")
	baselink=j[1].strip()
	folder=time.strftime(j[0].strip()+"_%d%m%Y")
	arr=getallids()
	links=arr["t"]
	print(len(links),"papers founded for",j[0])
	try:
		os.mkdir(folder)
	except Exception:
		pass
	for i in links:
		if links[i]+".pdf" in os.listdir(folder):
			print("Skiping as paper already present",links[i])
			continue
		try:
			print("downloading",links[i])
			obj=readwhere("https://epaper.dishadaily.com/pagemeta/get/"+getTodayId(i)+"/1-50",folder,links[i])
			obj.DownloadPaper()
			del obj
			gc.collect()
			print("Download completed for",links[i])
		except Exception as e:
			print(str(e))
			print("Send this screenshot to datta")
'''
links=arr["r"]
folder=time.strftime("%d%m%Y")
try:
	os.mkdir(folder)
except Exception:
	pass
for i in links:
	if links[i]+".pdf" in os.listdir(folder):
		print("Skiping as paper already present",links[i])
		continue
	try:
		print("downloading",links[i])
		obj=readwhere("https://epaper.dishadaily.com/pagemeta/get/"+str(i)+"/1-50",folder,links[i])
		obj.DownloadPaper()
		del obj
		gc.collect()
		print("Download completed for",links[i])
	except Exception as e:
		print(str(t))
		print("Send this screenshot to datta")
'''