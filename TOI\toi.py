import requests
import time
from PIL import Image
from io import BytesIO
import os
import concurrent.futures
import subprocess
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def create_folder(folder):
    """Create folder if it doesn't exist."""
    os.makedirs(folder, exist_ok=True)

# Common headers for requests
HEADERS = {
    "authority": "asset.harnscloud.com",
    "accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8",
    "accept-encoding": "gzip, deflate, br",
    "accept-language": "en-US,en;q=0.9",
    "origin": "https://epaper.timesgroup.com",
    "referer": "https://epaper.timesgroup.com/",
    "sec-fetch-dest": "image",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "cross-site",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36",
}

class TOI:
    def __init__(self, eid, name, folder, date=None):
        self.eid = eid[0]
        self.id = eid[1]
        self.name = name
        self.folder = folder
        self.date = date or time.strftime("%d_%m_%Y")
        self.quality = 60
        self.dateList = self.date.split("_")

    def download_image(self, no):
        """Download a single image with better error handling."""
        n = f"{no:02d}"  # Pad single-digit numbers with zero
        date = self.dateList
        eid = self.eid.replace("$date", self.date).replace("$no", n)
        url = f"https://asset.harnscloud.com/PublicationData/TOI/{self.id}/{date[2]}/{date[1]}/{date[0]}/Page/{eid}.jpg"
        
        try:
            response = requests.get(url, headers=HEADERS, timeout=10)
            response.raise_for_status()
            return (no, Image.open(BytesIO(response.content)))
        except requests.RequestException as e:
            # logging.warning(f"Failed to download image {no} for {self.name}: {e}")
            return None

    def download_papers(self):
        """Download newspaper pages with concurrent processing and ordered arrangement."""
        try:
            logging.info(f"Downloading started for {self.name}")
            
            # Use ThreadPoolExecutor for concurrent downloads
            with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
                # Try to download pages 1-30 concurrently
                futures = [executor.submit(self.download_image, no) for no in range(1, 31)]
                
                # Collect valid images and sort them
                imgs_with_order = [future.result() for future in concurrent.futures.as_completed(futures) if future.result() is not None]
                
                # Sort images by page number
                imgs_with_order.sort(key=lambda x: x[0])
                imgs = [img for _, img in imgs_with_order]
            
            if not imgs:
                logging.error(f"No images downloaded for {self.name}")
                return
            
            # Save first image and append others
            first_img = imgs[0]
            other_imgs = imgs[1:]
            
            output_path = os.path.join(self.folder, f"{self.name}.pdf")
            first_img.save(output_path, save_all=True, optimize=True, quality=self.quality, append_images=other_imgs)
            
            logging.info(f"Completed download, saved as: {output_path}")
        
        except Exception as e:
            logging.error(f"Failed to download for {self.name}: {e}")

def authenticate_user():
    """Authenticate and validate user."""
    allowed_users = ["datta", "santhi", "dwaraka", "madhu", "karthik1"]
    
    name = input("Enter User Name: ")
    if name not in allowed_users:
        logging.error(f"{name} has no access to this program")
        input("Press Enter to exit...")
        exit(1)
    
    # Device UUID validation
    link = f"https://temp-21dd4-default-rtdb.firebaseio.com/clients/uuid/{name}/.json"
    res = requests.get(link).text[1:-1]
    current_uuid = subprocess.check_output('wmic csproduct get UUID').decode("utf-8").split("\n")[1].strip()
    
    if res != current_uuid:
        # Send Telegram notification for new device
        method = "sendMessage"
        token = "1271352791:AAF2DSjJcLsE1BJpoh9CVuzKKlnaWSqfBss"
        requests.post(
            url=f'https://api.telegram.org/bot{token}/{method}',
            data={'chat_id': 512930889, 'text': f"{name} : {current_uuid} new device registration"}
        )
        logging.warning("User not registered for this device. Device registration request sent.")
        input("Press Enter to exit...")
        exit(1)
    
    return name

def main():
    # Create folder with current date
    folder = f"TOI{time.strftime('%d%m%Y')}"
    create_folder(folder)

    # Authenticate user
    authenticate_user()

    # Newspaper configurations
    papers = {
        "Hyderabad": ["$date_0$no_toih", "toih"],
        "Hyderabad_Times": ["$date_2$no_toih", "toih"],
        "The_Times_Of_Real Estate": ["$date_9$no_toih", "toih"],
        "Ahmedabad": ["$date_0$no_toiac", "toiac"],
        "Ahmedabad_Times": ["$date_2$no_toiac", "toiac"],
        "Bangalore": ["$date_0$no_toibgc", "toibgc"],
        "Bangalore_Times": ["$date_3$no_toibgc", "toibgc"],
        "Delhi": ["$date_0$no_cap", "cap"],
        "Delhi_Times": ["$date_2$no_cap", "cap"],
        "Chennai": ["$date_0$no_toich", "toich"],
        "Madras_Times": ["$date_2$no_toich", "toich"],
        "Mumbai": ["$date_0$no_toim", "toim"],
        "Bombay_Times": ["$date_2$no_toim", "toim"],
        "Lucknow": ["$date_0$no_toilc", "toilc"],
        "Lucknow_Times": ["$date_2$no_toilc", "toilc"],
        "Bhopal": ["$date_0$no_toibhoc", "toibhoc"],
        "Chandigarh": ["$date_0$no_toicgct", "toicgct"],
        "Chandigarh_Times": ["$date_2$no_toicgct", "toicgct"],
        "Goa": ["$date_0$no_toigo", "toigo"],
        "Jaipur": ["$date_0$no_toijc", "toijc"],
        "Kochi": ["$date_0$no_toikrko", "toikrko"],
        "Kolkata": ["$date_0$no_toikc", "toikc"],
        "Kolkata_Times": ["$date_2$no_toikc", "toikc"],
        "Pune": ["$date_0$no_toipuc", "toipuc"],
        "Pune_Times": ["$date_2$no_toipuc", "toipuc"],
    }

    # Download papers concurrently
    with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
        futures = []
        for name, config in papers.items():
            pdf_path = os.path.join(folder, f"{name}.pdf")
            if not os.path.exists(pdf_path):
                toi = TOI(config, name, folder)
                future = executor.submit(toi.download_papers)
                futures.append(future)
        
        # Wait for all downloads to complete
        concurrent.futures.wait(futures)

main()