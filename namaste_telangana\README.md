# Namaste Telangana Newspaper Downloader

This script downloads all newspapers from Namaste Telangana (epaper.ntnews.com) for a given date.

## Features

- Downloads all available editions for a specific date
- Merges individual pages into complete edition PDFs
- Organizes downloads by date and edition
- Supports downloading specific editions by ID
- Comprehensive logging and error handling
- Respectful rate limiting to avoid overwhelming the server

## Installation

1. Install required dependencies:
```bash
pip install -r requirements.txt
```

## Usage

### Download all editions for today:
```bash
python nt.py
```

### Download all editions for a specific date:
```bash
python nt.py --date "26/06/2025"
```

### Download to a specific output directory:
```bash
python nt.py --output "my_downloads"
```

### Download a specific edition by ID:
```bash
python nt.py --edition-id "1" --date "26/06/2025"
```

## Output Structure

```
output/
├── 26-06-2025/
│   ├── Hyderabad_Main/
│   │   └── NT_Hyderabad_Main_26-06-2025.pdf
│   ├── Telangana_Main/
│   │   └── NT_Telangana_Main_26-06-2025.pdf
│   └── ...
```

## API Endpoints Used

1. **Get Editions Hierarchy**: `GET /Home/GetEditionsHierarchy`
   - Retrieves all available editions and their IDs

2. **Get Edition Pages**: `POST /Home/GetAllpagespost`
   - Gets all pages for a specific edition and date

3. **Download Page PDF**: `GET /Home/downloadpdfedition_page`
   - Gets download information for a specific page

4. **Download PDF**: `GET /Home/Download`
   - Downloads the actual PDF file

## Notes

- The script includes delays between requests to be respectful to the server
- Individual page PDFs are automatically cleaned up after merging
- Failed downloads are logged and reported in the summary
- Date format should be DD/MM/YYYY
