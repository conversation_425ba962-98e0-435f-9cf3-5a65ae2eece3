import requests
import time
from PIL import Image
from io import BytesIO
import os
import threading
import subprocess
from datetime import datetime
import sys

# Color codes for terminal output
class Colors:
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

class Logger:
    @staticmethod
    def print_banner():
        banner = f"""
{Colors.HEADER}{Colors.BOLD}
╔══════════════════════════════════════════════════════════════╗
║                 ECONOMIC TIMES DOWNLOADER                    ║
║                    Enhanced Version v2.0                     ║
╚══════════════════════════════════════════════════════════════╝
{Colors.ENDC}"""
        print(banner)
    
    @staticmethod
    def info(message):
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"{Colors.OKCYAN}[{timestamp}] ℹ️  {message}{Colors.ENDC}")
    
    @staticmethod
    def success(message):
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"{Colors.OKGREEN}[{timestamp}] ✅ {message}{Colors.ENDC}")
    
    @staticmethod
    def warning(message):
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"{Colors.WARNING}[{timestamp}] ⚠️  {message}{Colors.ENDC}")
    
    @staticmethod
    def error(message):
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"{Colors.FAIL}[{timestamp}] ❌ {message}{Colors.ENDC}")
    
    @staticmethod
    def progress(current, total, task_name="", city=""):
        percentage = (current / total) * 100
        bar_length = 25
        filled_length = int(bar_length * current // total)
        bar = '█' * filled_length + '░' * (bar_length - filled_length)
        city_info = f" [{city}]" if city else ""
        print(f"\r{Colors.OKBLUE}📊 {task_name}{city_info} [{bar}] {percentage:.1f}% ({current}/{total}){Colors.ENDC}", end='', flush=True)
        if current == total:
            print()  # New line when complete

def createFolder(folder):
    try:
        if folder not in os.listdir():
            os.mkdir(folder)
            Logger.success(f"Created folder: {folder}")
        else:
            Logger.info(f"Folder already exists: {folder}")
    except Exception as e:
        Logger.error(f"Failed to create folder {folder}: {str(e)}")

headers = {
    "authority": "asset.harnscloud.com",
    "method": "GET",
    "path": "/PublicationData/ET/etmc/2022/01/01/Page/01_01_2022_025_etmc.jpg",
    "scheme": "https",
    "accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8",
    "accept-encoding": "gzip, deflate, br",
    "accept-language": "en-US,en;q=0.9",
    "origin": "https://epaper.timesgroup.com",
    "referer": "https://epaper.timesgroup.com/",
    'sec-ch-ua': '" Not A;Brand";v="99", "Chromium";v="96", "Google Chrome";v="96"',
    "sec-ch-ua-mobile": "?0",
    'sec-ch-ua-platform': '"Windows"',
    "sec-fetch-dest": "image",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "cross-site",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36",
}

class ET:
    def __init__(self, eid, name, folder, date=time.strftime("%d_%m_%Y")):
        self.eid = eid[0]
        self.id = eid[1]
        self.name = name
        self.folder = folder
        self.date = date
        self.quality = 60
        self.dateList = date.split("_")
        Logger.info(f"Initialized downloader for {Colors.BOLD}{name}{Colors.ENDC} edition")

    def doDownload(self):
        Logger.info(f"Starting page collection for {self.name}...")
        imgs = []
        successful_pages = 0
        
        for no in range(1, 31):
            if no < 10:
                n = "0" + str(no)
            else:
                n = str(no)
            
            date = self.dateList
            eid = self.eid.replace("$date", self.date).replace("$no", n)
            url = f"https://asset.harnscloud.com/PublicationData/ET/{self.id}/{date[2]}/{date[1]}/{date[0]}/Page/{eid}.jpg"
            
            try:
                response = requests.get(url, headers=headers, timeout=10)
                img = Image.open(BytesIO(response.content))
                imgs.append(img)
                successful_pages += 1
                Logger.progress(no, 30, "Collecting pages", self.name)
            except Exception as e:
                # Skip failed pages silently to avoid spam
                pass
        
        Logger.success(f"Successfully collected {successful_pages} pages for {self.name}")
        return imgs

    def downloadPapers(self):
        try:
            Logger.info(f"🚀 Starting download for {Colors.BOLD}{self.name}{Colors.ENDC} edition")
            
            imgs = self.doDownload()
            
            if not imgs:
                Logger.error(f"No pages found for {self.name}")
                return
            
            Logger.info(f"Generating PDF for {self.name}...")
            img = imgs[0]
            imgs.pop(0)
            
            pdf_path = f"{self.folder}/{self.name}.pdf"
            img.save(pdf_path, save_all=True, optimize=True, quality=int(self.quality), append_images=imgs)
            
            # Get file size for display
            file_size = os.path.getsize(pdf_path) / (1024 * 1024)  # MB
            Logger.success(f"PDF created: {Colors.BOLD}{self.name}.pdf{Colors.ENDC} ({file_size:.2f} MB)")
            
        except Exception as e:
            Logger.error(f"Failed to download {self.name}: {str(e)}")
            # Clean up partial file
            try:
                os.remove(f"{self.folder}/{self.name}.pdf")
                Logger.info("Cleaned up partial file")
            except:
                pass

def AuthDat():
    Logger.info("Authenticating with Garuda services...")
    name = "madhu"  # input("Enter Name:    ")
    password = "Happy@4578"  # input("Enter Password:    ")
    
    try:
        url = f"https://garudaauthservices.azurewebsites.net/api/validate?name={name}&password={password}&newspaper=ET"
        data = requests.get(url, timeout=10).json()
        
        if data["status"] != "Success":
            Logger.error(f"Authentication failed: {data['data']}")
            exit()
        
        Logger.success("Garuda authentication successful")
        return data["data"]
    except Exception as e:
        Logger.error(f"Authentication error: {str(e)}")
        exit()

def authUser():
    Logger.info("Starting user authentication...")
    name = input(f"{Colors.OKCYAN}Enter User Name: {Colors.ENDC}")
    users = ["datta", "santhi", "dwaraka", "madhu","karthik1"]
    
    if name not in users:
        Logger.error(f"User '{name}' has no access to this program")
        input("Press Enter to exit...")
        exit()
    
    Logger.info("Checking device registration...")
    try:
        link = f"https://temp-21dd4-default-rtdb.firebaseio.com/clients/uuid/{name}/.json"
        res = requests.get(link, timeout=10).text[1:-1]
        uuid = subprocess.check_output('wmic csproduct get UUID').decode("utf-8").split("\n")[1].strip()
        
        if res != uuid:
            method = "sendMessage"
            token = "1271352791:AAF2DSjJcLsE1BJpoh9CVuzKKlnaWSqfBss"
            requests.post(
                url=f'https://api.telegram.org/bot{token}/{method}',
                data={'chat_id': 512930889, 'text': f"{name} : {uuid} new device registration"}
            )
            Logger.error("Device not registered. Registration request sent to admin.")
            input("Press Enter to exit...")
            exit()
        
        Logger.success("User authentication successful!")
        return True
    except Exception as e:
        Logger.error(f"Authentication error: {str(e)}")
        exit()

def wait_for_downloads(threads, papers_info):
    """Wait for all download threads to complete with progress tracking"""
    Logger.info(f"Monitoring {len(threads)} download threads...")
    
    while threads:
        alive_threads = [t for t in threads if t.is_alive()]
        completed = len(threads) - len(alive_threads)
        
        if completed > 0:
            Logger.progress(completed, len(papers_info), "Overall Progress")
        
        threads = alive_threads
        time.sleep(1)
    
    Logger.progress(len(papers_info), len(papers_info), "Overall Progress")

def main():
    # Print banner
    Logger.print_banner()
    
    # Authentication
    authUser()
    
    # Create folder
    folder = "ET" + time.strftime("%d%m%Y")
    createFolder(folder)
    
    # Paper configurations
    papers = {
        "Mumbai": ["$date_0$no_etmc", "etmc"],
        "Bangalore": ["$date_0$no_etbg", "etbg"],
        "Delhi": ["$date_0$no_etdc", "etdc"],
        "Kolkata": ["$date_0$no_etkc", "etkc"]
    }
    
    Logger.info(f"Found {len(papers)} editions to process: {', '.join(papers.keys())}")
    
    threads = []
    papers_to_download = []
    
    # Check existing files and prepare downloads
    for city in papers:
        pdf_path = f"{folder}/{city}.pdf"
        if f"{city}.pdf" not in os.listdir(folder):
            papers_to_download.append(city)
            et_downloader = ET(papers[city], city, folder)
            thread = threading.Thread(target=et_downloader.downloadPapers)
            threads.append(thread)
            thread.start()
            Logger.info(f"Started download thread for {Colors.BOLD}{city}{Colors.ENDC}")
        else:
            Logger.warning(f"Skipping {city} - file already exists")
    
    if papers_to_download:
        Logger.info(f"Downloading {len(papers_to_download)} editions simultaneously...")
        
        # Wait for all downloads to complete
        wait_for_downloads(threads, papers_to_download)
        
        Logger.success("🎉 All downloads completed!")
        
        # Summary
        print(f"\n{Colors.OKCYAN}{'='*60}{Colors.ENDC}")
        Logger.info("Download Summary:")
        for city in papers_to_download:
            pdf_path = f"{folder}/{city}.pdf"
            if os.path.exists(pdf_path):
                file_size = os.path.getsize(pdf_path) / (1024 * 1024)
                Logger.success(f"{city}: {file_size:.2f} MB")
            else:
                Logger.error(f"{city}: Failed")
        print(f"{Colors.OKCYAN}{'='*60}{Colors.ENDC}")
    else:
        Logger.info("All files already exist. Nothing to download.")

if __name__ == "__main__":
    main()