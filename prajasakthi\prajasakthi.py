import webbrowser
import requests
from bs4 import BeautifulSoup
import json
from urllib.parse import urljoin
import os
from datetime import datetime
import time
import random

def get_headers(cf_clearance):
    return {
        'authority': 'epaper.prajasakti.com',
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        # 'accept-encoding': 'gzip, deflate, br, zstd',
        'accept-language': 'en-IN,en-GB;q=0.9,en-US;q=0.8,en;q=0.7,te;q=0.6,hi;q=0.5',
        'cache-control': 'max-age=0',
        'cookie': f'_ga=GA1.1.950300109.1750597716; _pk_id.24.86b6=e849186ada1bda40.1750597716.; csrftoken=v02KDDmhLMbI5rvJT0H5vsFFslVvGSOpQtQUTLNRjdBeNF4HPYFEphtRiyHqKGIG; _pk_ses.24.86b6=1; cf_clearance={cf_clearance}; _ga_S6RBYYBEYH=GS2.1.s1750877942$o2$g1$t1750878351$j60$l0$h0',
        'priority': 'u=0, i',
        'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-arch': '"x86"',
        'sec-ch-ua-bitness': '"64"',
        'sec-ch-ua-full-version': '"137.0.7151.120"',
        'sec-ch-ua-full-version-list': '"Google Chrome";v="137.0.7151.120", "Chromium";v="137.0.7151.120", "Not/A)Brand";v="********"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-model': '""',
        'sec-ch-ua-platform': '"Windows"',
        'sec-ch-ua-platform-version': '"19.0.0"',
        'sec-fetch-dest': 'document',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-site': 'none',
        'sec-fetch-user': '?1',
        'upgrade-insecure-requests': '1',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
    }

def get_newspaper_data(cf_clearance):
    headers = get_headers(cf_clearance)
    base_url = 'https://epaper.prajasakti.com/'
    
    try:
        # Add random delay to mimic human behavior
        time.sleep(random.uniform(1, 3))
        
        response = requests.get(base_url, headers=headers)
        
        if "Just a moment" in response.text:
            print("Cloudflare challenge detected. The cf_clearance cookie may have expired.")
            return None
        
        if response.status_code != 200:
            print(f"Failed to fetch data. Status code: {response.status_code}")
            return None
        
        soup = BeautifulSoup(response.text, 'html.parser')
        newspapers = []
        categories = soup.find_all('div', class_='cat-bg')
        
        for category in categories:
            category_name = category.find('h2').text.strip()
            newspapers_container = category.find_next_sibling('div', class_='row-custom')
            
            if not newspapers_container:
                continue
                
            items = newspapers_container.find_all('div', class_='col-custom')
            
            for item in items:
                link = item.find('a')
                if not link:
                    continue
                    
                href = link.get('href')
                full_url = urljoin(base_url, href)
                
                name_div = item.find('div', class_='edition-bg')
                if not name_div:
                    continue
                    
                name = name_div.text.strip()
                
                newspapers.append({
                    'category': category_name,
                    'name': name,
                    'url': full_url
                })
        
        return newspapers
    
    except Exception as e:
        print(f"An error occurred: {str(e)}")
        return None

def get_download_url(newspaper_url, headers):
    try:
        # Add random delay
        time.sleep(random.uniform(1, 3))
        
        response = requests.get(newspaper_url, headers=headers)
        
        if "Just a moment" in response.text:
            print("Cloudflare challenge detected when trying to get download URL")
            return None
        
        if response.status_code != 200:
            print(f"Failed to fetch newspaper page. Status code: {response.status_code}")
            return None
        
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Find the download button/link
        download_link = soup.find('a', href=lambda x: x and '/download_epaper/' in x)
        
        if download_link:
            download_path = download_link.get('href')
            return urljoin(newspaper_url, download_path)
        
        return None
    
    except Exception as e:
        print(f"Error getting download URL for {newspaper_url}: {str(e)}")
        return None

def download_newspaper(download_url, folder_path, newspaper_name, headers):
    try:
        # Create filename from newspaper name and current timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        safe_name = "".join(c for c in newspaper_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
        filename = f"{safe_name}_{timestamp}.pdf"
        filepath = os.path.join(folder_path, filename)
        
        print(f"Downloading {newspaper_name} from {download_url}...")
        
        # Add random delay
        time.sleep(random.uniform(1, 3))
        
        response = requests.get(download_url, headers=headers, stream=True)
        
        if "Just a moment" in response.text:
            print("Cloudflare challenge detected during download")
            return False
            
        if response.status_code == 200:
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(1024):
                    f.write(chunk)
            print(f"Successfully downloaded to {filepath}")
            return True
        else:
            print(f"Failed to download. Status code: {response.status_code}")
            return False
    
    except Exception as e:
        print(f"Error downloading {newspaper_name}: {str(e)}")
        return False

def main():
    print("Opening browser to help you get the cf_clearance cookie...")
    webbrowser.open("https://epaper.prajasakti.com/")
    
    cf_clearance = input("Please enter the cf_clearance token from your browser cookies: ").strip()
    
    newspapers = get_newspaper_data(cf_clearance)
    
    if not newspapers:
        print("Failed to extract newspaper data. The cf_clearance cookie may have expired.")
        return
    
    # Create download folder with today's date
    today = datetime.now().strftime("%d%m%Y")
    folder_name = f"prajasakthi_{today}"
    os.makedirs(folder_name, exist_ok=True)
    print(f"Created download folder: {folder_name}")
    
    headers = get_headers(cf_clearance)
    
    # Download newspapers
    downloaded_count = 0
    for paper in newspapers:
        paper_name = f"{paper['category']}_{paper['name']}"
        print(f"\nProcessing: {paper_name}")
        
        # Check if already downloaded
        existing_files = [f for f in os.listdir(folder_name) if paper_name in f]
        if existing_files:
            print(f"Already downloaded: {existing_files[0]}")
            continue
            
        # Get download URL
        download_url = get_download_url(paper['url'], headers)
        if not download_url:
            print(f"Could not find download URL for {paper_name}")
            continue
            
        # Download the newspaper
        if download_newspaper(download_url, folder_name, paper_name, headers):
            downloaded_count += 1
    
    print(f"\nDownload complete. {downloaded_count} new newspapers downloaded to {folder_name}/")
    print(f"Total newspapers available: {len(newspapers)}")

if __name__ == "__main__":
    main()