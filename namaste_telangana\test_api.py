#!/usr/bin/env python3
"""
Test script to verify Namaste Telangana API endpoints
"""

import requests
import json
from datetime import datetime

def test_editions_hierarchy():
    """Test the editions hierarchy API"""
    print("Testing editions hierarchy API...")
    
    url = "https://epaper.ntnews.com/Home/GetEditionsHierarchy"
    
    try:
        response = requests.get(url)
        response.raise_for_status()
        
        data = response.json()
        print(f"✓ Successfully fetched {len(data)} edition groups")
        
        # Print first few editions
        print("\nAvailable editions:")
        for group in data[:2]:  # Show first 2 groups
            print(f"  Group: {group['org_location']}")
            for edition in group.get('editionlocation', [])[:3]:  # Show first 3 editions
                print(f"    - {edition['Editionlocation']} (ID: {edition['EditionId']})")
        
        return True
        
    except Exception as e:
        print(f"✗ Error: {e}")
        return False

def test_edition_pages():
    """Test getting pages for a specific edition"""
    print("\nTesting edition pages API...")
    
    url = "https://epaper.ntnews.com/Home/GetAllpagespost"
    
    # Use today's date
    today = datetime.now()
    date_str = today.strftime("%d/%m/%Y")
    
    data = {
        'editionid': '1',  # Hyderabad Main
        'editiondate': date_str,
        'email': ''
    }
    
    try:
        response = requests.post(url, data=data)
        response.raise_for_status()
        
        pages = response.json()
        print(f"✓ Successfully fetched {len(pages)} pages for edition 1 on {date_str}")
        
        if pages:
            first_page = pages[0]
            print(f"  First page: {first_page['PageNumber']} (ID: {first_page['PageId']})")
        
        return True
        
    except Exception as e:
        print(f"✗ Error: {e}")
        return False

def test_download_info():
    """Test getting download info for a page"""
    print("\nTesting download info API...")
    
    # First get a page ID
    url = "https://epaper.ntnews.com/Home/GetAllpagespost"
    today = datetime.now()
    date_str = today.strftime("%d/%m/%Y")
    
    data = {
        'editionid': '1',
        'editiondate': date_str,
        'email': ''
    }
    
    try:
        response = requests.post(url, data=data)
        pages = response.json()
        
        if not pages:
            print("✗ No pages found to test download")
            return False
        
        page_id = pages[0]['PageId']
        
        # Now test download info
        download_url = "https://epaper.ntnews.com/Home/downloadpdfedition_page"
        params = {
            'id': page_id,
            'type': 1,
            'EditionId': '1',
            'Date': date_str
        }
        
        response = requests.get(download_url, params=params)
        response.raise_for_status()
        
        download_info = response.json()
        
        if download_info.get('status'):
            filename = download_info.get('FileName')
            print(f"✓ Successfully got download info for page {page_id}")
            print(f"  Filename: {filename}")
            return True
        else:
            print(f"✗ Download info request failed: {download_info}")
            return False
        
    except Exception as e:
        print(f"✗ Error: {e}")
        return False

def main():
    """Run all tests"""
    print("=== Namaste Telangana API Test ===\n")
    
    tests = [
        test_editions_hierarchy,
        test_edition_pages,
        test_download_info
    ]
    
    results = []
    for test in tests:
        results.append(test())
    
    print(f"\n=== Test Results ===")
    print(f"Passed: {sum(results)}/{len(results)}")
    
    if all(results):
        print("✓ All tests passed! The API is working correctly.")
    else:
        print("✗ Some tests failed. Check the errors above.")

if __name__ == "__main__":
    main()
