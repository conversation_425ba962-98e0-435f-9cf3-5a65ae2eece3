#!/usr/bin/env python3
"""
Example usage of the Namaste Telangana downloader
"""

from nt import NamasteTelanganaDownloader
from datetime import datetime, timedelta

def download_today():
    """Download all editions for today"""
    print("=== Downloading today's newspapers ===")
    
    downloader = NamasteTelanganaDownloader(base_dir="downloads")
    successful, failed = downloader.download_all_editions()
    
    print(f"Downloaded {len(successful)} editions successfully")
    if failed:
        print(f"Failed to download {len(failed)} editions: {failed}")

def download_specific_date():
    """Download all editions for a specific date"""
    print("=== Downloading newspapers for 26/06/2025 ===")
    
    downloader = NamasteTelanganaDownloader(base_dir="downloads")
    successful, failed = downloader.download_all_editions("26/06/2025")
    
    print(f"Downloaded {len(successful)} editions successfully")

def download_specific_edition():
    """Download a specific edition"""
    print("=== Downloading specific edition ===")
    
    downloader = NamasteTelanganaDownloader(base_dir="downloads")
    
    # First get all available editions
    editions = downloader.get_editions_hierarchy()
    
    # Find Hyderabad Main edition (ID: 1)
    target_edition = None
    for edition in editions:
        if edition['edition_id'] == '1':
            target_edition = edition
            break
    
    if target_edition:
        today = datetime.now().strftime("%d/%m/%Y")
        result = downloader.download_edition(target_edition, today)
        
        if result:
            print(f"Successfully downloaded: {result}")
        else:
            print("Failed to download the edition")
    else:
        print("Edition not found")

def list_available_editions():
    """List all available editions"""
    print("=== Available Editions ===")
    
    downloader = NamasteTelanganaDownloader()
    editions = downloader.get_editions_hierarchy()
    
    current_group = None
    for edition in editions:
        if edition['org_location'] != current_group:
            current_group = edition['org_location']
            print(f"\n{current_group}:")
        
        print(f"  - {edition['edition_name']} (ID: {edition['edition_id']})")

def main():
    """Main function with menu"""
    print("Namaste Telangana Downloader Examples")
    print("=====================================")
    
    while True:
        print("\nChoose an option:")
        print("1. List available editions")
        print("2. Download today's newspapers")
        print("3. Download newspapers for 26/06/2025")
        print("4. Download specific edition (Hyderabad Main)")
        print("5. Exit")
        
        choice = input("\nEnter your choice (1-5): ").strip()
        
        if choice == '1':
            list_available_editions()
        elif choice == '2':
            download_today()
        elif choice == '3':
            download_specific_date()
        elif choice == '4':
            download_specific_edition()
        elif choice == '5':
            print("Goodbye!")
            break
        else:
            print("Invalid choice. Please try again.")

if __name__ == "__main__":
    main()
