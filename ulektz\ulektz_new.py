import urllib.parse
import requests
from bs4 import BeautifulSoup
import re
import io
import base64
from requests.packages.urllib3.exceptions import InsecureRequestWarning
from PyPDF2 import Pdf<PERSON>eader, PdfMerger
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)



name="Thermodynamics"
fileName='index.html'


def decode_base64(encoded_str):
    decoded_bytes = base64.b64decode(encoded_str)
    decoded_str = decoded_bytes.decode('utf-8')
    return decoded_str

# Usage example:
# pdf_instance = fetch_pdf_instance("48285", "test1")
# Later, you can merge this instance with others using PdfMerger.
def fetch_pdf_instance(book_id,merger):
    session = requests.session()
    json_link = f"https://ereader.ulektz.com/reader/pdf/pdf_encrypt/decrypt.php?next_page=1&file_name=pass_{book_id}.epub&book_id={book_id}"
    data = session.get(json_link, verify=False).json()
    password = data["pass"]
    total_pages = data['total_pages']

    print(f"Number of pages found: {total_pages}")
    for i in range(1, int(total_pages) + 1):
        print(f"Processing page {i}")
        json_link = f"https://ereader.ulektz.com/reader/pdf/pdf_encrypt/decrypt.php?next_page={i}&file_name=pass_{book_id}.epub&book_id={book_id}"
        session.get(json_link)
        link = f"https://ereader.ulektz.com/reader/temp/extract/{book_id}/{i}.pdf"
        res = session.get(link)
        reader = PdfReader(io.BytesIO(res.content))
        reader.decrypt(password)
        merger.append(reader)

    return merger


# Usage example:
# readBooks('NDgyODV+NTI5OTEyNjd+cHVibGlzaGVyQm9vaw==', 'JAN 2020 SETS')
def readBooks(strend, bookName):
    # Define the URL
    url = f"https://ereader.ulektz.com/reader/read.php?{bookName}"
    # Encode the data
    encoded_data = urllib.parse.quote(strend)

    # Send the POST request
    response = requests.post(url, data={'data': encoded_data})
    return decode_base64(strend).split("~")[0]



# Usage example:
# results = extract_onclick_arguments('index.html')
# print(results)
def extract_onclick_arguments(file_path):
    # Load the HTML file
    with open(file_path, 'r', encoding='utf-8') as file:
        html_content = file.read()

    # Parse the HTML content
    soup = BeautifulSoup(html_content, 'html.parser')

    # Find all <a> tags with the class 'ripple-button'
    a_tags = soup.find_all('a', class_='ripple-button')

    # Regular expression to extract arguments from readBooks function in the onclick attribute
    pattern = re.compile(r"readBooks\('([^']+)',\s*'([^']+)'\)")

    # List to store the extracted arguments
    extracted_arguments = []

    # Iterate through the found <a> tags
    for tag in a_tags:
        onclick_attr = tag.get('onclick')
        if onclick_attr:
            match = pattern.search(onclick_attr)
            if match:
                # Append the extracted arguments as a list
                extracted_arguments.append([match.group(1), match.group(2)])

    return extracted_arguments


idsWithNames = extract_onclick_arguments(fileName)
pdfMergerInstance=[]
final_merger = PdfMerger()
for i in idsWithNames:
	decoded_id = readBooks(i[0],i[1])
	fetch_pdf_instance(decoded_id,final_merger)


final_merger.write("out/"+name+'.pdf')