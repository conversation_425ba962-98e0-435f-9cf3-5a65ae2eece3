import requests
from PyPDF2 import Pdf<PERSON><PERSON><PERSON>, PdfMerger
import io
from requests.packages.urllib3.exceptions import InsecureRequestWarning
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

_id="48285"
name="test1"
session=requests.session()
json_link="https://ereader.ulektz.com/reader/pdf/pdf_encrypt/decrypt.php?next_page=1&file_name=pass_"+_id+".epub&book_id="+_id
data=session.get(json_link,verify=False).json()
password=data["pass"]
merger = PdfMerger()

print("no of pages found : ",data['total_pages'])
for i in range(1,int(data['total_pages'])+1):
	print(i)
	json_link="https://ereader.ulektz.com/reader/pdf/pdf_encrypt/decrypt.php?next_page="+str(i)+"&file_name=pass_"+_id+".epub&book_id="+_id
	session.get(json_link)
	link="https://ereader.ulektz.com/reader/temp/extract/"+_id+"/"+str(i)+".pdf"
	res=session.get(link)
	reader=PdfReader(io.BytesIO(res.content))
	reader.decrypt(password)
	merger.append(reader)
merger.write("out/"+name+'.pdf')

'''
47423 with 32 pages
47424 with 10 pages
47606 with 426 pages
51462 with 222 pages'''
'''
50365 with 216 pages
50407 with 304 pages
50415 with 266 pages
51417 with 158 pages'''
'''
50415 with 266 pages
50419 with 240 pages
51475 with 160 pages
51476 with 144 pages
'''
'''
50365 with 216 pages
50399 with 240 pages
50407 with 304 pages
50415 with 266 pages
50419 with 240 pages
51417 with 158 pages
51559 with 144 pages
51566 with 96 pages'''
'''
50412
50417
50419
50420'''
