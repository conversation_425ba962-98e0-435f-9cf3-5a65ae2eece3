{"name": "prajasakthi-epaper-downloader", "private": true, "version": "1.0.0", "type": "module", "description": "Chrome extension to download Prajasakti epaper PDFs", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "build:watch": "tsc -b && vite build --watch", "build:dev": "tsc -b && vite build --mode development", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0", "lucide-react": "^0.469.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/chrome": "^0.0.277", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}