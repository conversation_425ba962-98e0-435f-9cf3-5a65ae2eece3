import requests
from PIL import Image
from io import BytesIO
import os
import threading
import time
import subprocess
from datetime import datetime
import sys

# Color codes for terminal output
class Colors:
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

class Logger:
    @staticmethod
    def print_banner():
        banner = f"""
{Colors.HEADER}{Colors.BOLD}
╔══════════════════════════════════════════════════════════════╗
║                   HINDUSTHAN TIMES DOWNLOADER                ║
║                     Enhanced Version v2.0                    ║
╚══════════════════════════════════════════════════════════════╝
{Colors.ENDC}"""
        print(banner)
    
    @staticmethod
    def info(message):
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"{Colors.OKCYAN}[{timestamp}] ℹ️  {message}{Colors.ENDC}")
    
    @staticmethod
    def success(message):
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"{Colors.OKGREEN}[{timestamp}] ✅ {message}{Colors.ENDC}")
    
    @staticmethod
    def warning(message):
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"{Colors.WARNING}[{timestamp}] ⚠️  {message}{Colors.ENDC}")
    
    @staticmethod
    def error(message):
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"{Colors.FAIL}[{timestamp}] ❌ {message}{Colors.ENDC}")
    
    @staticmethod
    def progress(current, total, task_name=""):
        percentage = (current / total) * 100
        bar_length = 30
        filled_length = int(bar_length * current // total)
        bar = '█' * filled_length + '░' * (bar_length - filled_length)
        print(f"\r{Colors.OKBLUE}📊 {task_name} [{bar}] {percentage:.1f}% ({current}/{total}){Colors.ENDC}", end='', flush=True)
        if current == total:
            print()  # New line when complete

class Hindusthan:
    def __init__(self, no, filename, quality):
        self.id = no
        Logger.info(f"Initializing download for ID: {no}")
        self.data = self.allPageDetails()
        self.name = filename
        self.quality = quality
        self.emptyImage = []
        for i in self.data:
            self.emptyImage.append(None)
        Logger.success(f"Found {len(self.data)} pages to download")

    def allPageDetails(self):
        try:
            Logger.info("Fetching page details from server...")
            url = f"https://epaper.hindustantimes.com/Home/GetAllpages?editionid={self.id}&editiondate={time.strftime('%d')}%2F{time.strftime('%m')}%2F{time.strftime('%Y')}"
            res = requests.get(url, timeout=8000).json()
            data = []
            for i in res:
                data.append("/".join(i["XHighResolution"].split("/")[:-1]) + "/" + i["FileName"].replace(".xml", ".jpg"))
            Logger.success("Page details fetched successfully")
            return data
        except Exception as e:
            Logger.error(f"Failed to fetch page details: {str(e)}")
            return []

    def formCompleteImg(self, url, no):
        headers = {
            'Referer': 'https://epaper.hindustantimes.com/', 
            'Sec-Fetch-Mode': 'no-cors', 
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/76.0.3809.100 Safari/537.36'
        }
        try:
            response = requests.get(url, headers=headers, timeout=8000)
            background = Image.open(BytesIO(response.content))
            self.emptyImage[no] = background
        except Exception as e:
            Logger.error(f"Failed to download page {no + 1}: {str(e)}")

    def DownloadPaper(self):
        Logger.info(f"Starting download of {len(self.data)} pages...")
        arr = []
        
        # Start all download threads
        for no, i in enumerate(self.data):
            t = threading.Thread(target=self.formCompleteImg, args=(i, no))
            arr.append(t)
            t.start()
        
        # Wait for all threads with progress indicator
        completed = 0
        for no, thread in enumerate(arr):
            thread.join()
            completed += 1
            Logger.progress(completed, len(arr), "Downloading pages")
        
        # Combine images into PDF
        Logger.info("Combining pages into PDF...")
        try:
            img = self.emptyImage[0]
            self.emptyImage.pop(0)
            img.save(self.name + '.pdf', save_all=True, optimize=True, quality=int(self.quality), append_images=self.emptyImage)
            Logger.success(f"PDF saved successfully: {self.name}.pdf")
        except Exception as e:
            Logger.error(f"Failed to create PDF: {str(e)}")
            raise e

def createFolder(folder):
    try:
        os.mkdir(folder)
        Logger.success(f"Created folder: {folder}")
    except FileExistsError:
        Logger.info(f"Folder already exists: {folder}")
    except Exception as e:
        Logger.error(f"Failed to create folder {folder}: {str(e)}")

def authUser():
    Logger.info("Starting user authentication...")
    name = input(f"{Colors.OKCYAN}Enter User Name: {Colors.ENDC}")
    users = ["datta", "santhi", "dwaraka","karthik1"]
    
    if name not in users:
        Logger.error(f"User '{name}' has no access to this program")
        input("Press Enter to exit...")
        exit()
    
    Logger.info("Checking device registration...")
    link = f"https://temp-21dd4-default-rtdb.firebaseio.com/clients/uuid/{name}/.json"
    res = requests.get(link).text[1:-1]
    uuid = subprocess.check_output('wmic csproduct get UUID').decode("utf-8").split("\n")[1].strip()
    
    if res != uuid:
        method = "sendMessage"
        token = "1271352791:AAF2DSjJcLsE1BJpoh9CVuzKKlnaWSqfBss"
        requests.post(
            url=f'https://api.telegram.org/bot{token}/{method}',
            data={'chat_id': 512930889, 'text': f"{name} : {uuid} new device registration"}
        )
        Logger.error("Device not registered. Registration request sent to admin.")
        input("Press Enter to exit...")
        exit()
    
    Logger.success("Authentication successful!")
    return True

def get_network(path):
    Logger.info("Connecting to internet and fetching configuration...")
    url1 = f'https://datta-papers-default-rtdb.firebaseio.com/{path}/.json'
    try:
        r1 = requests.get(url1, timeout=10)
        Logger.success("Configuration fetched successfully")
        return r1.json()
    except Exception as e:
        Logger.error(f"No internet connection or server error: {str(e)}")
        return 0

def main():
    # Print banner
    Logger.print_banner()
    
    # Uncomment the line below to enable authentication
    authUser()
    
    # Create main folder
    folder = "hindusthan" + time.strftime("_%d%m%Y")
    createFolder(folder)
    
    # Get network configuration
    dic = get_network("hindusthan_english")
    if not dic:
        Logger.error("Failed to get configuration. Exiting...")
        return
    
    total_editions = len(dic)
    processed_editions = 0
    
    Logger.info(f"Found {total_editions} editions to process")
    
    for i in dic:
        processed_editions += 1
        Logger.info(f"Processing edition: {Colors.BOLD}{i}{Colors.ENDC} ({processed_editions}/{total_editions})")
        
        createFolder(folder + "/" + i)
        
        edition_papers = len(dic[i])
        processed_papers = 0
        
        for j in dic[i]:
            processed_papers += 1
            paper_path = f"{folder}/{i}/{j}.pdf"
            
            Logger.info(f"📰 Processing paper: {Colors.BOLD}{j}{Colors.ENDC} ({processed_papers}/{edition_papers})")
            
            try:
                if j + ".pdf" in os.listdir(folder + "/" + i):
                    Logger.warning("Skipping - file already exists")
                    continue
                
                hin = Hindusthan(dic[i][j], folder + "/" + i + "/" + j, 50)
                hin.DownloadPaper()
                
            except Exception as e:
                Logger.error(f"Failed to process {i}/{j}: {str(e)}")
                try:
                    os.remove(paper_path)
                    Logger.info("Cleaned up partial file")
                except Exception:
                    pass
        
        Logger.success(f"Completed edition: {i}")
        print(f"{Colors.OKCYAN}{'='*60}{Colors.ENDC}")
    
    Logger.success("🎉 All downloads completed successfully!")

if __name__ == "__main__":
    main()