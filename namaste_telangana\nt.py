import requests
import json
import os
import time
from datetime import datetime
from PyPDF2 import PdfMerger
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class NamasteTelanganaDownloader:
    def __init__(self, base_dir="output"):
        self.base_url = "https://epaper.ntnews.com"
        self.session = requests.Session()
        self.base_dir = base_dir
        self.ensure_directory(self.base_dir)

        # Set headers to mimic a browser
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://epaper.ntnews.com/',
        })

    def ensure_directory(self, path):
        """Create directory if it doesn't exist"""
        if not os.path.exists(path):
            os.makedirs(path)
            logger.info(f"Created directory: {path}")

    def get_editions_hierarchy(self):
        """Get all available editions from the API"""
        try:
            url = f"{self.base_url}/Home/GetEditionsHierarchy"
            logger.info(f"Fetching editions hierarchy from: {url}")

            response = self.session.get(url)
            response.raise_for_status()

            editions_data = response.json()
            logger.info(f"Found {len(editions_data)} edition groups")

            # Extract all edition IDs and names
            all_editions = []
            for group in editions_data:
                for edition in group.get('editionlocation', []):
                    all_editions.append({
                        'edition_id': edition['EditionId'],
                        'edition_name': edition['Editionlocation'],
                        'org_location': edition['org_location']
                    })

            logger.info(f"Total editions found: {len(all_editions)}")
            return all_editions

        except Exception as e:
            logger.error(f"Error fetching editions hierarchy: {e}")
            return []

    def get_edition_pages(self, edition_id, date_str):
        """Get all pages for a specific edition and date"""
        try:
            url = f"{self.base_url}/Home/GetAllpagespost"

            # Format date as DD/MM/YYYY
            data = {
                'editionid': edition_id,
                'editiondate': date_str,
                'email': ''
            }

            logger.info(f"Fetching pages for edition {edition_id} on {date_str}")

            response = self.session.post(url, data=data)
            response.raise_for_status()

            pages_data = response.json()
            logger.info(f"Found {len(pages_data)} pages for edition {edition_id}")

            return pages_data

        except Exception as e:
            logger.error(f"Error fetching pages for edition {edition_id}: {e}")
            return []

    def download_page_pdf(self, page_id, edition_id, date_str):
        """Download PDF for a specific page"""
        try:
            # First, get the download filename
            download_url = f"{self.base_url}/Home/downloadpdfedition_page"
            params = {
                'id': page_id,
                'type': 1,
                'EditionId': edition_id,
                'Date': date_str
            }

            logger.info(f"Getting download info for page {page_id}")

            response = self.session.get(download_url, params=params)
            response.raise_for_status()

            download_info = response.json()

            if not download_info.get('status', False):
                logger.error(f"Failed to get download info for page {page_id}")
                return None

            filename = download_info.get('FileName')
            if not filename:
                logger.error(f"No filename returned for page {page_id}")
                return None

            # Now download the actual PDF
            pdf_download_url = f"{self.base_url}/Home/Download"
            pdf_params = {'Filename': filename}

            logger.info(f"Downloading PDF: {filename}")

            pdf_response = self.session.get(pdf_download_url, params=pdf_params)
            pdf_response.raise_for_status()

            return pdf_response.content, filename

        except Exception as e:
            logger.error(f"Error downloading page {page_id}: {e}")
            return None, None

    def merge_pages_to_edition_pdf(self, page_files, edition_name, date_str, output_dir):
        """Merge individual page PDFs into a single edition PDF"""
        try:
            merger = PdfMerger()

            # Sort page files by page number
            def get_page_sort_key(page_file):
                try:
                    return int(page_file['page_no'])
                except (ValueError, TypeError):
                    return 999  # Put non-numeric pages at the end

            page_files.sort(key=get_page_sort_key)

            for page_file in page_files:
                if os.path.exists(page_file['filepath']):
                    merger.append(page_file['filepath'])
                    logger.info(f"Added page {page_file['page_no']} to merger")

            # Create output filename
            safe_edition_name = "".join(c for c in edition_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
            safe_edition_name = safe_edition_name.replace(' ', '_')

            output_filename = f"NT_{safe_edition_name}_{date_str.replace('/', '-')}.pdf"
            output_path = os.path.join(output_dir, output_filename)

            # Write merged PDF
            with open(output_path, 'wb') as output_file:
                merger.write(output_file)

            merger.close()
            logger.info(f"Created merged PDF: {output_path}")

            # Clean up individual page files
            for page_file in page_files:
                if os.path.exists(page_file['filepath']):
                    os.remove(page_file['filepath'])
                    logger.info(f"Cleaned up: {page_file['filepath']}")

            return output_path

        except Exception as e:
            logger.error(f"Error merging pages for {edition_name}: {e}")
            return None

    def download_edition(self, edition_info, date_str):
        """Download all pages of a specific edition and merge them"""
        edition_id = edition_info['edition_id']
        edition_name = edition_info['edition_name']
        org_location = edition_info['org_location']

        logger.info(f"Starting download for edition: {edition_name} (ID: {edition_id})")

        # Create edition directory
        safe_edition_name = "".join(c for c in edition_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
        safe_edition_name = safe_edition_name.replace(' ', '_')

        edition_dir = os.path.join(self.base_dir, safe_edition_name)
        self.ensure_directory(edition_dir)

        # Get all pages for this edition
        pages = self.get_edition_pages(edition_id, date_str)

        if not pages:
            logger.warning(f"No pages found for edition {edition_name}")
            return None

        # Download each page
        page_files = []
        for page in pages:
            page_id = page['PageId']
            page_no = page['PageNo']

            logger.info(f"Downloading page {page_no} of {edition_name}")

            pdf_content, filename = self.download_page_pdf(page_id, edition_id, date_str)

            if pdf_content and filename:
                # Save page PDF - convert page_no to int for formatting
                try:
                    page_num = int(page_no)
                    page_filepath = os.path.join(edition_dir, f"page_{page_num:02d}_{filename}")
                except (ValueError, TypeError):
                    # Fallback if page_no is not a valid integer
                    page_filepath = os.path.join(edition_dir, f"page_{page_no}_{filename}")
                    page_num = page_no

                with open(page_filepath, 'wb') as f:
                    f.write(pdf_content)

                page_files.append({
                    'page_no': page_num,
                    'filepath': page_filepath,
                    'filename': filename
                })

                logger.info(f"Saved page {page_no}: {page_filepath}")

                # Add small delay to avoid overwhelming the server
                time.sleep(1)
            else:
                logger.error(f"Failed to download page {page_no} of {edition_name}")

        # Merge all pages into a single PDF
        if page_files:
            merged_pdf_path = self.merge_pages_to_edition_pdf(page_files, edition_name, date_str, edition_dir)
            return merged_pdf_path
        else:
            logger.error(f"No pages downloaded for edition {edition_name}")
            return None

    def download_all_editions(self, date_str=None):
        """Download all available editions for a given date"""
        if date_str is None:
            # Use today's date in DD/MM/YYYY format
            today = datetime.now()
            date_str = today.strftime("%d/%m/%Y")

        logger.info(f"Starting download for date: {date_str}")

        # Get all available editions
        editions = self.get_editions_hierarchy()

        if not editions:
            logger.error("No editions found")
            return

        # Create date-specific directory
        date_dir = os.path.join(self.base_dir, date_str.replace('/', '-'))
        self.ensure_directory(date_dir)

        # Update base_dir to date-specific directory
        original_base_dir = self.base_dir
        self.base_dir = date_dir

        successful_downloads = []
        failed_downloads = []

        # Download each edition
        for i, edition in enumerate(editions, 1):
            logger.info(f"Processing edition {i}/{len(editions)}: {edition['edition_name']}")

            try:
                merged_pdf = self.download_edition(edition, date_str)

                if merged_pdf:
                    successful_downloads.append({
                        'edition': edition['edition_name'],
                        'file': merged_pdf
                    })
                    logger.info(f"Successfully downloaded: {edition['edition_name']}")
                else:
                    failed_downloads.append(edition['edition_name'])
                    logger.error(f"Failed to download: {edition['edition_name']}")

                # Add delay between editions to be respectful to the server
                time.sleep(2)

            except Exception as e:
                logger.error(f"Error downloading edition {edition['edition_name']}: {e}")
                failed_downloads.append(edition['edition_name'])

        # Restore original base_dir
        self.base_dir = original_base_dir

        # Print summary
        logger.info(f"\n=== Download Summary for {date_str} ===")
        logger.info(f"Successful downloads: {len(successful_downloads)}")
        logger.info(f"Failed downloads: {len(failed_downloads)}")

        if successful_downloads:
            logger.info("\nSuccessfully downloaded editions:")
            for download in successful_downloads:
                logger.info(f"  - {download['edition']}: {download['file']}")

        if failed_downloads:
            logger.info("\nFailed to download editions:")
            for edition_name in failed_downloads:
                logger.info(f"  - {edition_name}")

        return successful_downloads, failed_downloads


def main():
    """Main function to run the downloader"""
    import argparse

    parser = argparse.ArgumentParser(description='Download Namaste Telangana newspapers')
    parser.add_argument('--date', type=str, help='Date in DD/MM/YYYY format (default: today)')
    parser.add_argument('--output', type=str, default='output', help='Output directory (default: output)')
    parser.add_argument('--edition-id', type=str, help='Download specific edition ID only')

    args = parser.parse_args()

    # Create downloader instance
    downloader = NamasteTelanganaDownloader(base_dir=args.output)

    if args.edition_id:
        # Download specific edition
        if not args.date:
            today = datetime.now()
            date_str = today.strftime("%d/%m/%Y")
        else:
            date_str = args.date

        # Get all editions to find the specific one
        editions = downloader.get_editions_hierarchy()
        target_edition = None

        for edition in editions:
            if edition['edition_id'] == args.edition_id:
                target_edition = edition
                break

        if target_edition:
            logger.info(f"Downloading specific edition: {target_edition['edition_name']}")
            merged_pdf = downloader.download_edition(target_edition, date_str)
            if merged_pdf:
                logger.info(f"Successfully downloaded: {merged_pdf}")
            else:
                logger.error("Failed to download the specified edition")
        else:
            logger.error(f"Edition ID {args.edition_id} not found")
    else:
        # Download all editions
        downloader.download_all_editions(args.date)


if __name__ == "__main__":
    main()