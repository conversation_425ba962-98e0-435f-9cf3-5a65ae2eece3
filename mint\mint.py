import requests
from bs4 import BeautifulSoup
import time
import os

link="https://epaper.livemint.com/"
todayDate = time.strftime("%d%m%Y")
folderName = "mint_"+todayDate

if not os.path.exists(folderName):
    os.makedirs(folderName)

response = requests.get(link)

soup = BeautifulSoup(response.content, 'html.parser')

options={}
for i in soup.findAll('option'):
    options[i.text.strip()]=i['value']

# {'Delhi': '1', 'Mumbai': '2', 'Ahmedabad': '3', 'Bengaluru': '4', 'Chennai': '5', 'Hyderabad': '6', 'Kolkata': '7'}

for city in options:
    # print("Downloading "+city)
    pdfLink = f"https://epaper.livemint.com/downloadPdf.php?filepath=EPAPERIMAGES/{todayDate}/mint_{city.lower()}.PDF&filename=mint_{city.lower()}.PDF"
    print(pdfLink)
    continue
    if os.path.exists(f"{folderName}/{city}.pdf"):
        print("Already Downloaded "+city)
        continue
    
    try:
        with requests.get(pdfLink, stream=True) as r:
            r.raise_for_status()
            with open(f"{folderName}/{city}.pdf", 'wb') as f:
                for chunk in r.iter_content(chunk_size=8192):
                    f.write(chunk)
    except:
        print("Error in downloading "+city)    