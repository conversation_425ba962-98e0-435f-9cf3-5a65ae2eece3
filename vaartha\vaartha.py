import concurrent.futures
import requests
from bs4 import BeautifulSoup
import os
import datetime
import subprocess


today = datetime.date.today().strftime("%Y-%m-%d")

def get_pdf_link(link):
    response = requests.get(link)
    soup = BeautifulSoup(response.text, 'html.parser')
    pdf_link = soup.find("a", {"class": "btn-pdfdownload"})["href"]
    return pdf_link


def get_paper_links():
    link="https://epaper.vaartha.com/"
    response = requests.get(link)
    soup = BeautifulSoup(response.text, 'html.parser')
    paper_links={}
    soups=[]
    for i in soup.findAll("li", {"class": "nav-item"}):
        soups.append(i)
    
    for i in soups:
        papers = i.findAll("a", {"class": "dropdown-item"})

        folder_links = {}
        for j in papers:
            folder_links[j.text.strip()] = "https://epaper.vaartha.com"+j["href"]
        if (folder_links!={}):
            paper_links[i.find("a").text]=folder_links
    return paper_links

groups=get_paper_links()


def authUser():
	global name
	name=input("Enter User Name :")
	users=["datta","karthik1","santhi","dwaraka"]
	if name not in users:
		print(name,"have no access for this program")
		input()
		exit()
	link="https://temp-21dd4-default-rtdb.firebaseio.com/clients/uuid/"+name+"/.json"
	res=requests.get(link).text[1:-1]
	uuid=subprocess.check_output('wmic csproduct get UUID').decode("utf-8").split("\n")[1].strip()
	if res!=uuid:
		method = "sendMessage"
		token = "1271352791:AAF2DSjJcLsE1BJpoh9CVuzKKlnaWSqfBss"
		requests.post(
		    url='https://api.telegram.org/bot{0}/{1}'.format(token, method),
		    data={'chat_id': 512930889, 'text': name+" : "+uuid+" new device registration"}
		)
		print("User doesn't register for this device, Althrough this device registration request will be sent")
		input()
		exit()
	return True

authUser()


output_folder = f"prabhatKhabar_{today}"
if not os.path.exists(output_folder):
    os.makedirs(output_folder)



for group_name, group in groups.items():
    print(f"Downloading group: {group_name}")
    group_folder = os.path.join(output_folder, group_name)
    if not os.path.exists(group_folder):
        os.makedirs(group_folder)

    with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
        futures = {
            executor.submit(
                lambda pdf_name, pdf_link: (
                    pdf_name,
                    get_pdf_link(pdf_link),
                ),
                pdf_name,
                pdf_link,
            )
            for pdf_name, pdf_link in group.items()
        }
        for future in concurrent.futures.as_completed(futures):
            try:
                pdf_name, pdf_link = future.result()
                pdf_path = os.path.join(group_folder, f"{pdf_name}.pdf")
                if not os.path.exists(pdf_path):
                    print(f"Downloading {pdf_name}")
                    response = requests.get(pdf_link)
                    with open(pdf_path, "wb") as f:
                        f.write(response.content)
                else:
                    print(f"Skipping {pdf_name} as {pdf_path} already exists")
            except Exception as e:
                print(f"An error occurred while downloading {pdf_name}: {e}")


