// Content script for Prajasakti Epaper Downloader

// Listen for messages from popup/background
chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
  if (request.action === 'extractPDFLink') {
    const pdfLink = extractPDFDownloadLink();
    sendResponse({ success: true, pdfLink });
  }

  if (request.action === 'getCurrentPageInfo') {
    const pageInfo = getCurrentPageInfo();
    sendResponse({ success: true, pageInfo });
  }
});

// Extract PDF download link from the current page
function extractPDFDownloadLink(): string | null {
  // Look for download link with 'download_epaper' in href
  const downloadLink = document.querySelector('a[href*="download_epaper"]') as HTMLAnchorElement;
  
  if (downloadLink) {
    const href = downloadLink.href;
    // Convert relative URL to absolute if needed
    if (href.startsWith('/')) {
      return window.location.origin + href;
    }
    return href;
  }
  
  return null;
}

// Get current page information
function getCurrentPageInfo() {
  const url = window.location.href;
  const urlParams = new URLSearchParams(window.location.search);
  
  return {
    url,
    date: urlParams.get('date'),
    edition: urlParams.get('edition'),
    pageNo: urlParams.get('pg_no'),
    title: document.title
  };
}

// Notify that content script is loaded
console.log('Prajasakti Epaper Downloader content script loaded');
