// Background script for Prajasakti Epaper Downloader

interface DownloadRequest {
  url: string;
  filename: string;
  cookies: string;
}

interface EpaperData {
  date: string;
  editions: Array<{
    id: string;
    name: string;
    url: string;
  }>;
}

// Listen for messages from popup
chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
  console.log('🔧 Background: Received message:', request);

  if (request.action === 'getCookies') {
    console.log('🍪 Background: Getting cookies for URL:', request.url);
    getCookiesForSite(request.url)
      .then(cookies => {
        console.log('🍪 Background: Successfully got cookies, length:', cookies.length);
        sendResponse({ success: true, cookies });
      })
      .catch(error => {
        console.error('🍪 Background: Failed to get cookies:', error);
        sendResponse({ success: false, error: error.message });
      });
    return true; // Keep message channel open for async response
  }

  if (request.action === 'downloadPDF') {
    downloadPDF(request.data)
      .then(result => sendResponse({ success: true, result }))
      .catch(error => sendResponse({ success: false, error: error.message }));
    return true;
  }

  if (request.action === 'fetchEpaperData') {
    fetchEpaperData(request.url, request.cookies)
      .then(data => sendResponse({ success: true, data }))
      .catch(error => sendResponse({ success: false, error: error.message }));
    return true;
  }

  if (request.action === 'extractPDFLink') {
    extractPDFLink(request.url, request.cookies)
      .then(pdfLink => sendResponse({ success: true, pdfLink }))
      .catch(error => sendResponse({ success: false, error: error.message }));
    return true;
  }
});

// Get cookies for the specified site
async function getCookiesForSite(url: string): Promise<string> {
  console.log('🍪 getCookiesForSite: Fetching cookies for domain based on URL:', url);
  try {
    // Get cookies for both the main domain and subdomains
    const domains = [
      'prajasakti.com',
      '.prajasakti.com',
      'epaper.prajasakti.com',
      '.epaper.prajasakti.com'
    ];

    let allCookies: chrome.cookies.Cookie[] = [];

    // Collect cookies from all relevant domains
    for (const domain of domains) {
      try {
        const domainCookies = await chrome.cookies.getAll({
          domain: domain.startsWith('.') ? domain.substring(1) : domain,
          partitionKey: {}
        });
        console.log(`🍪 getCookiesForSite: Found ${domainCookies.length} cookies for domain "${domain}"`);

        // Log each cookie for this domain
        domainCookies.forEach(cookie => {
          console.log(`🍪 Domain "${domain}" cookie:`, {
            name: cookie.name,
            domain: cookie.domain,
            path: cookie.path,
            secure: cookie.secure,
            httpOnly: cookie.httpOnly,
            sameSite: cookie.sameSite,
            value: cookie.value.substring(0, 20) + '...'
          });
        });

        allCookies = allCookies.concat(domainCookies);
      } catch (error) {
        console.warn(`🍪 getCookiesForSite: Failed to get cookies for domain "${domain}":`, error);
      }
    }

    // Specifically check for cf_clearance cookie
    console.log('🔍 Searching for cf_clearance cookie...');
    const cfClearance = allCookies.find(c => c.name === 'cf_clearance');
    if (cfClearance) {
      console.log('✅ Found cf_clearance cookie:', {
        domain: cfClearance.domain,
        path: cfClearance.path,
        secure: cfClearance.secure,
        httpOnly: cfClearance.httpOnly,
        sameSite: cfClearance.sameSite,
        value: cfClearance.value.substring(0, 50) + '...'
      });
    } else {
      console.warn('❌ cf_clearance cookie NOT found in collected cookies');

      // Try to find it with a broader search
      console.log('🔍 Trying broader search for cf_clearance...');
      try {
        const allSiteCookies = await chrome.cookies.getAll({});
        const cfCookies = allSiteCookies.filter(c => c.name === 'cf_clearance');
        console.log(`🔍 Found ${cfCookies.length} cf_clearance cookies across all sites:`);
        cfCookies.forEach(cookie => {
          console.log('🔍 cf_clearance found on:', {
            name: cookie.name,
            domain: cookie.domain,
            path: cookie.path,
            secure: cookie.secure,
            httpOnly: cookie.httpOnly,
            sameSite: cookie.sameSite,
            value: cookie.value.substring(0, 50) + '...'
          });
        });

        // Add any cf_clearance cookies that match our domains
        const relevantCfCookies = cfCookies.filter(c =>
          c.domain.includes('prajasakti.com') ||
          c.domain === '.prajasakti.com' ||
          c.domain === 'prajasakti.com'
        );
        if (relevantCfCookies.length > 0) {
          console.log('✅ Adding relevant cf_clearance cookies to collection');
          allCookies = allCookies.concat(relevantCfCookies);
        }
      } catch (error) {
        console.error('🔍 Error in broader cf_clearance search:', error);
      }
    }

    // Remove duplicates based on name and domain
    const uniqueCookies = allCookies.filter((cookie, index, self) =>
      index === self.findIndex(c => c.name === cookie.name && c.domain === cookie.domain)
    );

    console.log(`🍪 getCookiesForSite: Total unique cookies found: ${uniqueCookies.length}`);

    if (uniqueCookies.length === 0) {
      console.warn('🍪 getCookiesForSite: No cookies found via chrome.cookies API. Trying fallback...');
      try {
        console.log('🍪 getCookiesForSite: Attempting fallback to content script...');
        const contentCookies = await getCookiesFromContentScript();
        if (contentCookies) {
             console.log('🍪 getCookiesForSite: Successfully got cookies from content script fallback.');
             return contentCookies;
        }
      } catch (error) {
         console.error('🍪 getCookiesForSite: Content script fallback failed.', error);
      }
      return ""; // Return empty string if no cookies are found
    }

    // Additional attempt to get cf_clearance specifically
    console.log('🔍 Making additional attempt to find cf_clearance cookie...');
    try {
      // Try different URL variations for cf_clearance
      const cfUrls = [
        'https://prajasakti.com',
        'https://www.prajasakti.com',
        'https://epaper.prajasakti.com'
      ];

      for (const cfUrl of cfUrls) {
        try {
          const cfCookies = await chrome.cookies.getAll({
            url: cfUrl,
            name: 'cf_clearance'
          });

          if (cfCookies.length > 0) {
            console.log(`✅ Found cf_clearance cookie for URL ${cfUrl}:`, {
              domain: cfCookies[0].domain,
              path: cfCookies[0].path,
              value: cfCookies[0].value.substring(0, 50) + '...'
            });

            // Add to our collection if not already present
            const alreadyExists = uniqueCookies.some(c =>
              c.name === 'cf_clearance' && c.domain === cfCookies[0].domain
            );

            if (!alreadyExists) {
              console.log('➕ Adding cf_clearance to cookie collection');
              uniqueCookies.push(cfCookies[0]);
            }
            break;
          }
        } catch (error) {
          console.warn(`🔍 Failed to get cf_clearance for ${cfUrl}:`, error);
        }
      }
    } catch (error) {
      console.error('🔍 Error in cf_clearance specific search:', error);
    }

    // Log the details for debugging (important cookies first)
    const importantCookies = uniqueCookies.filter(c =>
      c.name.includes('csrf') ||
      c.name.includes('session') ||
      c.name.includes('auth') ||
      c.name.includes('cf_clearance') ||
      c.name.includes('_pk_') ||
      c.name.includes('_ga')
    );

    console.log('🍪 getCookiesForSite: Important cookies found:', importantCookies.map(c => ({
      name: c.name,
      value: c.value.substring(0, 15) + '...',
      domain: c.domain,
      httpOnly: c.httpOnly,
      secure: c.secure
    })));

    console.log('🍪 getCookiesForSite: All cookie details:', uniqueCookies.map(c => ({
      name: c.name,
      value: c.value.substring(0, 15) + '...',
      domain: c.domain,
      httpOnly: c.httpOnly,
      secure: c.secure
    })));

    // Format into a cookie string
    const cookieString = uniqueCookies
      .map(cookie => `${cookie.name}=${cookie.value}`)
      .join('; ');

    console.log('🍪 getCookiesForSite: Final cookie string length:', cookieString.length);
    console.log('🍪 getCookiesForSite: Cookie string preview:', cookieString.substring(0, 200) + '...');

    // Log all cookie names in the final string
    const cookieNames = uniqueCookies.map(c => c.name);
    console.log('🍪 getCookiesForSite: Final cookie names:', cookieNames);

    // Specifically check if cf_clearance is in the final string
    const hasCfClearance = cookieString.includes('cf_clearance=');
    console.log('🔍 Final cookie string contains cf_clearance:', hasCfClearance);

    if (hasCfClearance) {
      const cfMatch = cookieString.match(/cf_clearance=([^;]+)/);
      if (cfMatch) {
        console.log('✅ cf_clearance value in final string:', cfMatch[1].substring(0, 50) + '...');
      }
    } else {
      console.warn('❌ cf_clearance is missing from final cookie string!');
    }

    return cookieString;

  } catch (error) {
    console.error('🍪 getCookiesForSite: A critical error occurred:', error);
    throw new Error(`Failed to get cookies: ${error}`);
  }
}


// Get cookies from content script (fallback method)
async function getCookiesFromContentScript(): Promise<string> {
  try {
    // Get the active tab
    const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
    if (tabs.length === 0) {
      throw new Error('No active tab found');
    }

    const tab = tabs[0];
    if (!tab.id) {
      throw new Error('No tab ID found');
    }

    console.log('🍪 getCookiesFromContentScript: Injecting script into tab:', tab.url);

    // Inject script to get document.cookie
    const results = await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      func: () => {
        console.log('🍪 Content script: Getting document.cookie');
        const cookies = document.cookie;
        console.log('🍪 Content script: Found cookies:', cookies.length, 'chars');
        return cookies;
      }
    });

    if (results && results[0] && results[0].result) {
      return results[0].result;
    }

    throw new Error('No cookies returned from content script');
  } catch (error) {
    console.error('🍪 getCookiesFromContentScript: Error:', error);
    throw error;
  }
}

// Download PDF with cookies
async function downloadPDF(data: DownloadRequest): Promise<string> {
  try {
    console.log('📥 Background: Starting PDF download from:', data.url);
    console.log('🍪 Background: Using cookies (length):', data.cookies.length);
    console.log('📁 Background: Target filename:', data.filename);

    const headers = {
      'Cookie': data.cookies,
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      'Accept': 'application/pdf,application/octet-stream,*/*',
      'Accept-Language': 'en-US,en;q=0.5',
      'Accept-Encoding': 'gzip, deflate, br',
      'DNT': '1',
      'Connection': 'keep-alive',
      'Upgrade-Insecure-Requests': '1',
      'Sec-Fetch-Dest': 'document',
      'Sec-Fetch-Mode': 'navigate',
      'Sec-Fetch-Site': 'same-origin'
    };

    const response = await fetch(data.url, { headers });

    console.log('📥 Background: Download response status:', response.status, response.statusText);
    console.log('📥 Background: Content-Type:', response.headers.get('content-type'));
    console.log('📥 Background: Content-Length:', response.headers.get('content-length'));

    if (!response.ok) {
      const errorText = await response.text();
      console.error('📥 Background: Download error response:', errorText.substring(0, 500));
      throw new Error(`HTTP error! status: ${response.status} - ${response.statusText}`);
    }

    const blob = await response.blob();
    console.log('📥 Background: Downloaded blob size:', blob.size, 'bytes');
    console.log('📥 Background: Blob type:', blob.type);

    const url = URL.createObjectURL(blob);

    const downloadId = await chrome.downloads.download({
      url: url,
      filename: data.filename,
      saveAs: true
    });

    console.log('📥 Background: Download started with ID:', downloadId);
    return `Download started with ID: ${downloadId}`;
  } catch (error) {
    console.error('📥 Background: Error downloading PDF:', error);
    throw error;
  }
}

// Fetch epaper data from the website
async function fetchEpaperData(url: string, cookies: string): Promise<EpaperData> {
  try {
    console.log('📰 Background: Fetching epaper data from:', url);
    console.log('🍪 Background: Using cookies (length):', cookies.length);
    console.log('🍪 Background: Cookie preview:', cookies.substring(0, 100) + '...');

    // Check if cf_clearance is in the cookie string
    const hasCfClearance = cookies.includes('cf_clearance=');
    console.log('🔍 Background: cf_clearance in cookie string:', hasCfClearance);

    if (hasCfClearance) {
      const cfMatch = cookies.match(/cf_clearance=([^;]+)/);
      if (cfMatch) {
        console.log('✅ Background: cf_clearance value:', cfMatch[1].substring(0, 50) + '...');
      }
    } else {
      console.warn('❌ Background: cf_clearance missing from cookie string!');
      console.log('🍪 Background: Full cookie string:', cookies);
    }

    const headers: Record<string, string> = {
      'Cookie': cookies,
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
      'Accept-Language': 'en-US,en;q=0.5',
      'Accept-Encoding': 'gzip, deflate, br',
      'DNT': '1',
      'Connection': 'keep-alive',
      'Upgrade-Insecure-Requests': '1',
      'Sec-Fetch-Dest': 'document',
      'Sec-Fetch-Mode': 'navigate',
      'Sec-Fetch-Site': 'none'
    };

    console.log('📰 Background: Request headers:', Object.keys(headers));
    console.log('🍪 Background: Cookie header being sent:', headers.Cookie.substring(0, 200) + '...');

    // Try alternative approach: Use chrome.cookies API to ensure cookies are set
    console.log('🔧 Background: Attempting to ensure cookies are properly set...');
    try {
      // Parse the cookie string and try to set each cookie using Chrome API
      const cookiePairs = cookies.split(';').map(c => c.trim());
      for (const pair of cookiePairs) {
        const [name, value] = pair.split('=');
        if (name && value && name.trim() === 'cf_clearance') {
          console.log('🔧 Background: Attempting to set cf_clearance cookie via Chrome API');
          try {
            await chrome.cookies.set({
              url: url,
              name: name.trim(),
              value: value.trim(),
              domain: '.prajasakti.com',
              path: '/',
              secure: true,
              httpOnly: true,
              sameSite: 'no_restriction'
            });
            console.log('✅ Background: Successfully set cf_clearance via Chrome API');
          } catch (cookieError) {
            console.warn('⚠️ Background: Failed to set cf_clearance via Chrome API:', cookieError);
          }
        }
      }
    } catch (cookieSetError) {
      console.warn('⚠️ Background: Error in cookie setting process:', cookieSetError);
    }

    // Use a unique identifier for this request to track it
    const requestId = `epaper-request-${Date.now()}-${Math.random()}`;
    console.log('🔧 Background: Setting up request with ID:', requestId);

    // Store the cookies for this request
    (globalThis as any).pendingRequests = (globalThis as any).pendingRequests || new Map();
    (globalThis as any).pendingRequests.set(requestId, cookies);

    // Add the request ID as a header so we can identify it in the webRequest listener
    headers['X-Request-ID'] = requestId;

    const response = await fetch(url, { headers });

    // Clean up the stored cookies
    (globalThis as any).pendingRequests.delete(requestId);

    console.log('📰 Background: Response status:', response.status, response.statusText);
    console.log('📰 Background: Response headers:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const errorText = await response.text();
      console.error('📰 Background: Error response body:', errorText.substring(0, 500));
      throw new Error(`HTTP error! status: ${response.status} - ${response.statusText}`);
    }

    const html = await response.text();
    console.log('📰 Background: Received HTML, length:', html.length);
    console.log('📰 Background: HTML preview:', html.substring(0, 200) + '...');

    return parseEpaperHTML(html, url);
  } catch (error) {
    console.error('📰 Background: Error fetching epaper data:', error);
    throw error;
  }
}

// Extract PDF download link from edition page
async function extractPDFLink(editionUrl: string, cookies: string): Promise<string | null> {
  try {
    console.log('🔗 Background: Extracting PDF link from:', editionUrl);

    const response = await fetch(editionUrl, {
      headers: {
        'Cookie': cookies,
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
      }
    });

    if (!response.ok) {
      console.error('🔗 Background: HTTP error:', response.status, response.statusText);
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const html = await response.text();
    console.log('🔗 Background: Received HTML, length:', html.length);

    // Parse HTML using regex instead of DOMParser (service worker doesn't have DOM APIs)
    console.log('🔗 Background: Parsing HTML for PDF links using regex...');

    // Look for download links using regex patterns
    const downloadPatterns = [
      // Pattern 1: href with download_epaper
      /<a[^>]*href\s*=\s*["']([^"']*download_epaper[^"']*)["'][^>]*>/gi,
      // Pattern 2: href with download
      /<a[^>]*href\s*=\s*["']([^"']*download[^"']*)["'][^>]*>/gi,
      // Pattern 3: onclick with download
      /<a[^>]*onclick\s*=\s*["'][^"']*download[^"']*["'][^>]*href\s*=\s*["']([^"']*)["'][^>]*>/gi,
      // Pattern 4: any link with .pdf
      /<a[^>]*href\s*=\s*["']([^"']*\.pdf[^"']*)["'][^>]*>/gi,
      // Pattern 5: links with download in title or text
      /<a[^>]*title\s*=\s*["'][^"']*download[^"']*["'][^>]*href\s*=\s*["']([^"']*)["'][^>]*>/gi
    ];

    for (const pattern of downloadPatterns) {
      let match;
      while ((match = pattern.exec(html)) !== null) {
        let href = match[1];

        if (href) {
          // Convert relative URL to absolute if needed
          if (href.startsWith('/')) {
            href = new URL(editionUrl).origin + href;
          } else if (href.startsWith('./')) {
            href = new URL(href, editionUrl).href;
          }

          console.log('🔗 Background: Found potential PDF link:', href);

          // Validate that it looks like a reasonable download link
          if (href.includes('download') || href.includes('.pdf') || href.includes('epaper')) {
            console.log('🔗 Background: Validated PDF link:', href);
            return href;
          }
        }
      }
    }

    // If no specific download links found, look for any PDF files
    const pdfRegex = /<a[^>]*href\s*=\s*["']([^"']*\.pdf[^"']*)["'][^>]*>/gi;
    let match;
    while ((match = pdfRegex.exec(html)) !== null) {
      let href = match[1];

      if (href.startsWith('/')) {
        href = new URL(editionUrl).origin + href;
      } else if (href.startsWith('./')) {
        href = new URL(href, editionUrl).href;
      }

      console.log('🔗 Background: Found PDF file link:', href);
      return href;
    }

    // Last resort: look for any links containing "download" text
    const downloadTextRegex = /<a[^>]*href\s*=\s*["']([^"']*)["'][^>]*>([^<]*download[^<]*)<\/a>/gi;
    while ((match = downloadTextRegex.exec(html)) !== null) {
      let href = match[1];
      const linkText = match[2];

      if (href.startsWith('/')) {
        href = new URL(editionUrl).origin + href;
      } else if (href.startsWith('./')) {
        href = new URL(href, editionUrl).href;
      }

      console.log('🔗 Background: Found download text link:', href, 'with text:', linkText.trim());
      return href;
    }

    console.warn('🔗 Background: No PDF download link found in the page');
    return null;
  } catch (error) {
    console.error('🔗 Background: Error extracting PDF link:', error);
    throw error;
  }
}

// Parse HTML to extract edition information and PDF links (without DOMParser)
function parseEpaperHTML(html: string, baseUrl: string): EpaperData {
  console.log('📰 Background: Parsing HTML without DOMParser...');
  console.log('📰 Background: HTML length:', html.length);
  console.log('📰 Background: HTML preview (first 1000 chars):', html.substring(0, 1000));

  // Extract date from URL
  const urlParams = new URLSearchParams(new URL(baseUrl).search);
  const date = urlParams.get('date') || new Date().toISOString().split('T')[0];

  console.log('📰 Background: Extracted date:', date);

  // Find edition links using regex instead of DOMParser
  const editions: Array<{ id: string; name: string; url: string }> = [];

  // First, let's search for any occurrence of "active_edition" to see what patterns exist
  const activeEditionMatches = html.match(/active_edition[^)]*\)/gi);
  console.log('📰 Background: Found active_edition patterns:', activeEditionMatches);

  // Look for links with onclick="active_edition(...)" pattern - exact match to Python code
  const editionLinkRegex = /<a[^>]*onclick\s*=\s*["']active_edition\(["'](\d+)["']\)["'][^>]*>(.*?)<\/a>/gi;

  let match;
  while ((match = editionLinkRegex.exec(html)) !== null) {
    const editionId = match[1];
    const linkContent = match[2];

    // Extract text content from the link (remove any HTML tags)
    const editionName = linkContent.replace(/<[^>]*>/g, '').trim() || `Edition ${editionId}`;
    const editionUrl = `${new URL(baseUrl).origin}/view?date=${date}&edition=${editionId}&pg_no=1`;

    console.log('📰 Background: Found edition (pattern 1):', { id: editionId, name: editionName, url: editionUrl });

    editions.push({
      id: editionId,
      name: editionName,
      url: editionUrl
    });
  }

  // If no editions found with the first pattern, try alternative patterns
  if (editions.length === 0) {
    console.log('📰 Background: No editions found with primary pattern, trying alternatives...');

    // Pattern 2: Try pattern without quotes around the parameter
    const altRegex1 = /<a[^>]*onclick\s*=\s*["']active_edition\((\d+)\)["'][^>]*>(.*?)<\/a>/gi;
    while ((match = altRegex1.exec(html)) !== null) {
      const editionId = match[1];
      const linkContent = match[2];
      const editionName = linkContent.replace(/<[^>]*>/g, '').trim() || `Edition ${editionId}`;
      const editionUrl = `${new URL(baseUrl).origin}/view?date=${date}&edition=${editionId}&pg_no=1`;

      console.log('📰 Background: Found edition (pattern 2):', { id: editionId, name: editionName, url: editionUrl });

      editions.push({
        id: editionId,
        name: editionName,
        url: editionUrl
      });
    }
  }

  // Pattern 3: Try with single quotes
  if (editions.length === 0) {
    console.log('📰 Background: Trying pattern 3 (single quotes)...');
    const altRegex2 = /<a[^>]*onclick\s*=\s*['"]active_edition\(['"](\d+)['"][)]['"][^>]*>(.*?)<\/a>/gi;
    while ((match = altRegex2.exec(html)) !== null) {
      const editionId = match[1];
      const linkContent = match[2];
      const editionName = linkContent.replace(/<[^>]*>/g, '').trim() || `Edition ${editionId}`;
      const editionUrl = `${new URL(baseUrl).origin}/view?date=${date}&edition=${editionId}&pg_no=1`;

      console.log('📰 Background: Found edition (pattern 3):', { id: editionId, name: editionName, url: editionUrl });

      editions.push({
        id: editionId,
        name: editionName,
        url: editionUrl
      });
    }
  }

  // Pattern 4: More flexible pattern
  if (editions.length === 0) {
    console.log('📰 Background: Trying pattern 4 (flexible)...');
    const altRegex3 = /onclick\s*=\s*["'][^"']*active_edition\([^)]*["']?(\d+)["']?[^)]*\)[^"']*["'][^>]*>(.*?)<\/a>/gi;
    while ((match = altRegex3.exec(html)) !== null) {
      const editionId = match[1];
      const linkContent = match[2];
      const editionName = linkContent.replace(/<[^>]*>/g, '').trim() || `Edition ${editionId}`;
      const editionUrl = `${new URL(baseUrl).origin}/view?date=${date}&edition=${editionId}&pg_no=1`;

      console.log('📰 Background: Found edition (pattern 4):', { id: editionId, name: editionName, url: editionUrl });

      editions.push({
        id: editionId,
        name: editionName,
        url: editionUrl
      });
    }
  }

  // If still no editions found, let's look for any links that might be editions
  if (editions.length === 0) {
    console.log('📰 Background: No editions found with any pattern. Searching for any edition-related links...');

    // Look for any links with "edition" in href or text
    const editionLinkRegex = /<a[^>]*(?:href\s*=\s*["'][^"']*edition[^"']*["']|[^>]*edition[^>]*)>(.*?)<\/a>/gi;
    while ((match = editionLinkRegex.exec(html)) !== null) {
      console.log('📰 Background: Found potential edition link:', match[0]);
    }

    // Look for any onclick handlers
    const onclickRegex = /<a[^>]*onclick\s*=\s*["']([^"']*)["'][^>]*>(.*?)<\/a>/gi;
    let onclickCount = 0;
    while ((match = onclickRegex.exec(html)) !== null && onclickCount < 10) {
      console.log('📰 Background: Found onclick link:', match[1], '|', match[2].replace(/<[^>]*>/g, '').trim());
      onclickCount++;
    }
  }

  // Remove duplicates
  const uniqueEditions = editions.filter((edition, index, self) =>
    index === self.findIndex(e => e.id === edition.id)
  );

  console.log('📰 Background: Final editions found:', uniqueEditions.length);

  return {
    date,
    editions: uniqueEditions
  };
}

// Set up webRequest listener to modify Cookie headers
chrome.webRequest.onBeforeSendHeaders.addListener(
  (details) => {
    console.log('🌐 WebRequest: Intercepting request to:', details.url);

    // Check if this is one of our requests
    const requestIdHeader = details.requestHeaders?.find(h => h.name === 'X-Request-ID');
    if (!requestIdHeader) {
      return {}; // Not our request, don't modify
    }

    const requestId = requestIdHeader.value;
    console.log('🌐 WebRequest: Found our request ID:', requestId);

    // Get the stored cookies for this request
    const pendingRequests = (globalThis as any).pendingRequests || new Map();
    const storedCookies = pendingRequests.get(requestId);

    if (!storedCookies) {
      console.warn('🌐 WebRequest: No stored cookies found for request ID:', requestId);
      return {};
    }

    console.log('🌐 WebRequest: Modifying Cookie header with stored cookies');
    console.log('🍪 WebRequest: Stored cookies length:', storedCookies.length);

    // Find and replace the Cookie header
    const headers = details.requestHeaders || [];
    let cookieHeaderFound = false;

    for (const header of headers) {
      if (header.name.toLowerCase() === 'cookie') {
        console.log('🍪 WebRequest: Original Cookie header:', header.value?.substring(0, 100) + '...');
        header.value = storedCookies;
        console.log('🍪 WebRequest: Modified Cookie header:', storedCookies.substring(0, 100) + '...');
        cookieHeaderFound = true;
        break;
      }
    }

    // If no Cookie header was found, add one
    if (!cookieHeaderFound) {
      console.log('🍪 WebRequest: Adding new Cookie header');
      headers.push({
        name: 'Cookie',
        value: storedCookies
      });
    }

    // Remove our custom header
    const filteredHeaders = headers.filter(h => h.name !== 'X-Request-ID');

    return { requestHeaders: filteredHeaders };
  },
  {
    urls: [
      "https://epaper.prajasakti.com/*",
      "https://prajasakti.com/*",
      "https://www.prajasakti.com/*"
    ]
  },
  ["blocking", "requestHeaders"]
);

// Initialize extension
chrome.runtime.onInstalled.addListener(() => {
  console.log('Prajasakti Epaper Downloader installed');
});
