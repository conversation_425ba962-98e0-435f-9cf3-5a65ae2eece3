// Chrome API utilities

import type { ChromeMessage, ChromeResponse, EpaperData, DownloadRequest } from '../types';

// Send message to background script
export function sendMessageToBackground(message: ChromeMessage): Promise<ChromeResponse> {
  return new Promise((resolve, reject) => {
    console.log('📨 Sending message to background:', message);
    chrome.runtime.sendMessage(message, (response: ChromeResponse) => {
      if (chrome.runtime.lastError) {
        console.error('📨 Chrome runtime error:', chrome.runtime.lastError.message);
        reject(new Error(chrome.runtime.lastError.message));
      } else {
        console.log('📨 Background response received:', response);
        resolve(response);
      }
    });
  });
}

// Send message to content script
export function sendMessageToContent(tabId: number, message: ChromeMessage): Promise<ChromeResponse> {
  return new Promise((resolve, reject) => {
    chrome.tabs.sendMessage(tabId, message, (response: ChromeResponse) => {
      if (chrome.runtime.lastError) {
        reject(new Error(chrome.runtime.lastError.message));
      } else {
        resolve(response);
      }
    });
  });
}

// Get cookies for the current site
export async function getCookies(url: string): Promise<string> {
  try {
    console.log('🍪 getCookies: Requesting cookies for URL:', url);
    const response = await sendMessageToBackground({
      action: 'getCookies',
      url
    });

    console.log('🍪 getCookies: Background response:', response);

    if (response.success) {
      const cookies = response.cookies || '';
      console.log('🍪 getCookies: Success, cookies length:', cookies.length);
      return cookies;
    } else {
      console.error('🍪 getCookies: Failed with error:', response.error);
      throw new Error(response.error || 'Failed to get cookies');
    }
  } catch (error) {
    console.error('🍪 getCookies: Exception:', error);
    throw error;
  }
}

// Fetch epaper data
export async function fetchEpaperData(url: string, cookies: string): Promise<EpaperData> {
  try {
    const response = await sendMessageToBackground({
      action: 'fetchEpaperData',
      url,
      cookies
    });
    
    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.error || 'Failed to fetch epaper data');
    }
  } catch (error) {
    console.error('Error fetching epaper data:', error);
    throw error;
  }
}

// Download PDF
export async function downloadPDF(downloadData: DownloadRequest): Promise<string> {
  try {
    const response = await sendMessageToBackground({
      action: 'downloadPDF',
      data: downloadData
    });
    
    if (response.success) {
      return response.result || 'Download started';
    } else {
      throw new Error(response.error || 'Failed to download PDF');
    }
  } catch (error) {
    console.error('Error downloading PDF:', error);
    throw error;
  }
}

// Get current active tab
export function getCurrentTab(): Promise<chrome.tabs.Tab> {
  return new Promise((resolve, reject) => {
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      if (chrome.runtime.lastError) {
        reject(new Error(chrome.runtime.lastError.message));
      } else if (tabs.length > 0) {
        resolve(tabs[0]);
      } else {
        reject(new Error('No active tab found'));
      }
    });
  });
}

// Extract PDF link from current page
export async function extractPDFLink(): Promise<string | null> {
  try {
    const tab = await getCurrentTab();
    if (!tab.id) {
      throw new Error('No tab ID found');
    }

    const response = await sendMessageToContent(tab.id, {
      action: 'extractPDFLink'
    });
    
    if (response.success) {
      return response.pdfLink || null;
    } else {
      throw new Error(response.error || 'Failed to extract PDF link');
    }
  } catch (error) {
    console.error('Error extracting PDF link:', error);
    return null;
  }
}
