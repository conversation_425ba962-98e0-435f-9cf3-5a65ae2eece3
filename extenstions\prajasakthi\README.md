# Prajasakti Epaper Downloader - Chrome Extension

A Chrome extension built with React + TypeScript to download Prajasakti epaper PDFs with automatic cookie extraction.

## Features

- 🍪 Automatic cookie extraction from epaper.prajasakti.com
- 📅 Date picker with default to current date
- 📁 Optional download path selection
- 📰 Multiple edition support
- 🔄 Bulk download functionality
- ✅ Download progress tracking

## Setup Instructions

### 1. Install Dependencies

```bash
npm install
```

### 2. Build the Extension

```bash
npm run build
```

### 3. Add Icons (Required)

Create the following icon files in the `public` folder:
- `icon16.png` (16x16 pixels)
- `icon48.png` (48x48 pixels)
- `icon128.png` (128x128 pixels)

You can use any PNG images or create simple icons with the Prajasakti logo.

### 4. Load Extension in Chrome

1. Open Chrome and go to `chrome://extensions/`
2. Enable "Developer mode" (toggle in top right)
3. Click "Load unpacked"
4. Select the `dist` folder (created after running `npm run build`)

### 5. Usage

1. Navigate to `https://epaper.prajasakti.com/`
2. Click the extension icon in the Chrome toolbar
3. The extension will automatically extract cookies
4. Select your desired date (defaults to today)
5. Choose editions to download
6. Click "Download Selected PDFs"

## Development

### Run in Development Mode

```bash
npm run dev
```

### Build for Production

```bash
npm run build
```

### Project Structure

```
src/
├── App.tsx              # Main React component
├── background.ts        # Chrome extension background script
├── content.ts          # Content script for cookie extraction
├── types.ts            # TypeScript type definitions
└── utils/
    ├── chrome-api.ts   # Chrome API utilities
    └── date.ts         # Date utility functions
```

## Technical Details

- **Framework**: React 19 + TypeScript
- **Build Tool**: Vite
- **Chrome Extension**: Manifest V3
- **Permissions**: activeTab, cookies, downloads, storage, scripting

## Troubleshooting

### Extension Not Working
- Ensure you're on `epaper.prajasakti.com` when using the extension
- Check that all required permissions are granted
- Reload the extension if cookies aren't being extracted

### Download Issues
- Verify you have the latest cookies extracted
- Check that the selected date has available editions
- Ensure Chrome has permission to download files

### Build Issues
- Run `npm install` to ensure all dependencies are installed
- Check that Node.js version is compatible (16+ recommended)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test the extension thoroughly
5. Submit a pull request

## License

This project is for educational purposes. Please respect the terms of service of epaper.prajasakti.com.
