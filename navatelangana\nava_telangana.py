import requests
import os
import datetime
import time
import json
import subprocess
from PyPDF2 import Pd<PERSON><PERSON><PERSON><PERSON>, PdfReader
from io import BytesIO
import concurrent.futures

# --- Configuration ---
BASE_URL_HIERARCHY = "https://epaper.navatelangana.com/Home/GetEditionsHierarchy"
BASE_URL_GET_PAGES = "https://epaper.navatelangana.com/Home/GetAllpagespost"
BASE_URL_DOWNLOAD_INFO = "https://epaper.navatelangana.com/Home/downloadpdfedition_page"
DATE_FORMAT_API = "%d/%m/%Y"
DATE_FORMAT_FOLDER = "%d%m%Y"

# --- Helper Functions ---
def sanitize_filename(name):
    """Removes characters potentially problematic for filenames."""
    # Replace spaces and invalid chars. Keep it simple for this case.
    name = name.replace(' ', '_')
    # Remove characters not typically allowed in filenames on common OS
    invalid_chars = '<>:"/\\|?*'
    for char in invalid_chars:
        name = name.replace(char, '')
    return name

def get_today_date_str(format_string):
    """Gets today's date in the specified format."""
    return datetime.datetime.now().strftime(format_string)

# --- Main Logic ---
def download_epapers():
    """Fetches edition list and downloads PDFs for today."""
    today_date_api_str = get_today_date_str(DATE_FORMAT_API)
    today_date_folder_str = get_today_date_str(DATE_FORMAT_FOLDER)
    output_base_dir = f"NavaTelangana_ePapers_{today_date_folder_str}"
    
    print(f"Attempting to download Nava Telangana epapers for: {today_date_api_str}")
    print(f"Downloads will be saved in base folder: {output_base_dir}")
    print("-" * 30)
    
    # Get Editions Hierarchy
    try:
        print(f"Fetching edition list from: {BASE_URL_HIERARCHY}")
        response_hierarchy = requests.get(BASE_URL_HIERARCHY, timeout=30)
        response_hierarchy.raise_for_status()
        editions_data = response_hierarchy.json()
        print("Successfully fetched edition list.")
    except Exception as e:
        print(f"Error fetching edition list: {e}")
        return
    
    os.makedirs(output_base_dir, exist_ok=True)
    
    download_count = 0
    skipped_count = 0
    error_count = 0
    
    # Process each edition group
    for edition_group in editions_data:
        org_location = edition_group.get("org_location")
        edition_locations = edition_group.get("editionlocation")
        
        if not org_location or not edition_locations:
            continue
            
        folder_name = sanitize_filename(org_location)
        folder_path = os.path.join(output_base_dir, folder_name)
        os.makedirs(folder_path, exist_ok=True)
        
        print(f"\nProcessing Group: {org_location}")
        
        # Process each edition
        for edition in edition_locations:
            edition_name = edition.get("Editionlocation")
            edition_id = edition.get("EditionId")
            
            if not edition_name or not edition_id:
                continue
                
            print(f"  Checking Edition: {edition_name} (ID: {edition_id})")
            
            output_filename = f"{sanitize_filename(edition_name)}.pdf"
            output_filepath = os.path.join(folder_path, output_filename)
            
            if os.path.exists(output_filepath):
                print(f"    Skipping: File already exists at {output_filepath}")
                skipped_count += 1
                continue
            
            try:
                # Get all pages for this edition
                print(f"    Fetching page list for edition ID: {edition_id}")
                page_data = {
                    "editionid": edition_id,
                    "editiondate": today_date_api_str,
                    "email": ""
                }
                
                response_pages = requests.post(BASE_URL_GET_PAGES, json=page_data, timeout=30)
                response_pages.raise_for_status()
                pages = response_pages.json()
                
                if not pages:
                    print(f"    No pages found for edition: {edition_name}")
                    error_count += 1
                    continue
                    
                print(f"    Found {len(pages)} pages to download")
                
                # Download each page and merge into a single PDF
                merger = PdfMerger()
                
                # Use ThreadPoolExecutor for concurrent downloads
                with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
                    futures = []
                    
                    for page in pages:
                        page_id = page.get("PageId")
                        page_no = page.get("PageNo")
                        
                        if not page_id:
                            continue
                            
                        futures.append(
                            executor.submit(
                                download_single_page,
                                page_id,
                                edition_id,
                                today_date_api_str,
                                page_no
                            )
                        )
                    
                    # Process completed downloads
                    page_pdfs = []
                    for future in concurrent.futures.as_completed(futures):
                        result = future.result()
                        if result:
                            page_no, pdf_data = result
                            page_pdfs.append((int(page_no), pdf_data))
                
                # Sort pages by page number and add to merger
                page_pdfs.sort(key=lambda x: x[0])
                for _, pdf_data in page_pdfs:
                    merger.append(PdfReader(BytesIO(pdf_data)))
                
                # Save the merged PDF
                if len(page_pdfs) > 0:
                    merger.write(output_filepath)
                    print(f"    Successfully downloaded and merged {len(page_pdfs)} pages to: {output_filepath}")
                    download_count += 1
                else:
                    print(f"    Failed to download any pages for {edition_name}")
                    error_count += 1
                
            except Exception as e:
                print(f"    Error processing {edition_name}: {e}")
                error_count += 1
                # Clean up partially downloaded file
                if os.path.exists(output_filepath):
                    try:
                        os.remove(output_filepath)
                    except:
                        pass

def download_single_page(page_id, edition_id, date_str, page_no):
    """Download a single page PDF and return its data"""
    try:
        params = {
            'id': page_id,
            'type': '1',
            'EditionId': edition_id,
            'Date': date_str
        }
        
        response_info = requests.get(BASE_URL_DOWNLOAD_INFO, params=params, timeout=30)
        response_info.raise_for_status()
        download_info = response_info.json()
        
        if download_info.get("status") and download_info.get("FileName"):
            pdf_filename = download_info["FileName"]
            pdf_download_url = f"https://epaper.navatelangana.com/Home/Download?Filename={pdf_filename}"
            
            response_pdf = requests.get(pdf_download_url, timeout=30)
            response_pdf.raise_for_status()
            
            return (page_no, response_pdf.content)
    except Exception as e:
        print(f"      Error downloading page {page_no} (ID: {page_id}): {e}")
    
    return None

def authUser():
    global name
    name=input("Enter User Name :")
    users=["datta","karthik1"] #"santhi","dwaraka",
    if name not in users:
        print(name,"have no access for this program")
        input()
        exit()
    link="https://temp-21dd4-default-rtdb.firebaseio.com/clients/uuid/"+name+"/.json"
    res=requests.get(link).text[1:-1]
    uuid=subprocess.check_output('wmic csproduct get UUID').decode("utf-8").split("\n")[1].strip()
    if res!=uuid:
        method = "sendMessage"
        token = "1271352791:AAF2DSjJcLsE1BJpoh9CVuzKKlnaWSqfBss"
        requests.post(
            url='https://api.telegram.org/bot{0}/{1}'.format(token, method),
            data={'chat_id': 512930889, 'text': name+" : "+uuid+" new device registration"}
        )
        print("User doesn't register for this device, Althrough this device registration request will be sent")
        input()
        exit()
    return True

authUser()

download_epapers()
