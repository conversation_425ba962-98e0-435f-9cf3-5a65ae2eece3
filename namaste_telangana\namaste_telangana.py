import requests
import os
import datetime
import time
import subprocess
from PyPDF2 import Pdf<PERSON><PERSON><PERSON>, PdfReader

from colorama import Fore, Back, Style, init
from tqdm import tqdm
import threading

# Initialize colorama for cross-platform colored output
init(autoreset=True)

# --- Configuration ---
BASE_URL_HIERARCHY = "https://epaper.ntnews.com/Home/GetEditionsHierarchy"
BASE_URL_GET_PAGES = "https://epaper.ntnews.com/Home/GetAllpagespost"
BASE_URL_DOWNLOAD_INFO = "https://epaper.ntnews.com/Home/downloadpdfedition_page"
DATE_FORMAT_API = "%d/%m/%Y"
DATE_FORMAT_FOLDER = "%d%m%Y"

# --- Enhanced Logging System ---
class EPaperLogger:
    """Enhanced logging system with colors, progress bars, and structured output"""

    def __init__(self):
        self.lock = threading.Lock()
        self.current_progress = None
        self.stats = {
            'downloads': 0,
            'skipped': 0,
            'errors': 0,
            'total_pages': 0
        }

    def _get_timestamp(self):
        return datetime.datetime.now().strftime("%H:%M:%S")

    def _print_with_lock(self, message):
        with self.lock:
            if self.current_progress:
                self.current_progress.write(message)
            else:
                print(message)

    def header(self, title):
        """Print a styled header"""
        border = "═" * (len(title) + 4)
        self._print_with_lock(f"\n{Fore.CYAN}{border}")
        self._print_with_lock(f"{Fore.CYAN}║ {Fore.WHITE}{Style.BRIGHT}{title} {Fore.CYAN}║")
        self._print_with_lock(f"{Fore.CYAN}{border}{Style.RESET_ALL}")

    def info(self, message, indent=0):
        """Print info message with timestamp"""
        prefix = "  " * indent
        timestamp = self._get_timestamp()
        self._print_with_lock(f"{Fore.BLUE}[{timestamp}]{Style.RESET_ALL} {prefix}{Fore.WHITE}{message}")

    def success(self, message, indent=0):
        """Print success message"""
        prefix = "  " * indent
        timestamp = self._get_timestamp()
        self._print_with_lock(f"{Fore.GREEN}[{timestamp}] ✓{Style.RESET_ALL} {prefix}{Fore.GREEN}{message}")

    def warning(self, message, indent=0):
        """Print warning message"""
        prefix = "  " * indent
        timestamp = self._get_timestamp()
        self._print_with_lock(f"{Fore.YELLOW}[{timestamp}] ⚠{Style.RESET_ALL} {prefix}{Fore.YELLOW}{message}")

    def error(self, message, indent=0):
        """Print error message"""
        prefix = "  " * indent
        timestamp = self._get_timestamp()
        self._print_with_lock(f"{Fore.RED}[{timestamp}] ✗{Style.RESET_ALL} {prefix}{Fore.RED}{message}")

    def section(self, title, indent=0):
        """Print section header"""
        prefix = "  " * indent
        self._print_with_lock(f"\n{prefix}{Fore.MAGENTA}▶ {Style.BRIGHT}{title}{Style.RESET_ALL}")

    def subsection(self, title, indent=1):
        """Print subsection header"""
        prefix = "  " * indent
        self._print_with_lock(f"{prefix}{Fore.CYAN}├─ {title}{Style.RESET_ALL}")

    def detail(self, key, value, indent=2):
        """Print key-value detail"""
        prefix = "  " * indent
        self._print_with_lock(f"{prefix}{Fore.BLUE}│  {key}:{Style.RESET_ALL} {value}")

    def progress_bar(self, total, desc="Processing"):
        """Create a progress bar"""
        self.current_progress = tqdm(
            total=total,
            desc=f"{Fore.GREEN}{desc}{Style.RESET_ALL}",
            bar_format="{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}]",
            ncols=80,
            colour='green'
        )
        return self.current_progress

    def update_stats(self, stat_type, increment=1):
        """Update statistics"""
        if stat_type in self.stats:
            self.stats[stat_type] += increment

    def print_summary(self):
        """Print final summary"""
        self.header("DOWNLOAD SUMMARY")
        self._print_with_lock(f"{Fore.GREEN}✓ Successfully Downloaded: {self.stats['downloads']} editions")
        self._print_with_lock(f"{Fore.YELLOW}⚠ Skipped (Already Exists): {self.stats['skipped']} editions")
        self._print_with_lock(f"{Fore.RED}✗ Failed Downloads: {self.stats['errors']} editions")
        self._print_with_lock(f"{Fore.BLUE}📄 Total Pages Processed: {self.stats['total_pages']} pages")

        total_attempted = self.stats['downloads'] + self.stats['errors']
        if total_attempted > 0:
            success_rate = (self.stats['downloads'] / total_attempted) * 100
            self._print_with_lock(f"{Fore.CYAN}📊 Success Rate: {success_rate:.1f}%")

# Initialize global logger
logger = EPaperLogger()

# --- Helper Functions ---
def sanitize_filename(name):
    """Removes characters potentially problematic for filenames."""
    # Replace spaces and invalid chars. Keep it simple for this case.
    name = name.replace(' ', '_')
    # Remove characters not typically allowed in filenames on common OS
    invalid_chars = '<>:"/\\|?*'
    for char in invalid_chars:
        name = name.replace(char, '')
    return name

def get_today_date_str(format_string):
    """Gets today's date in the specified format."""
    return datetime.datetime.now().strftime(format_string)

def validate_pdf_pages(filepath, expected_page_count):
    """Validate that the merged PDF has the correct number of pages"""
    try:
        with open(filepath, 'rb') as file:
            pdf_reader = PdfReader(file)
            actual_pages = len(pdf_reader.pages)

            if actual_pages != expected_page_count:
                logger.warning(f"PDF page count mismatch: expected {expected_page_count}, got {actual_pages}", indent=4)
                return False
            else:
                logger.info(f"PDF validation successful: {actual_pages} pages", indent=4)
                return True

    except Exception as e:
        logger.error(f"PDF validation failed: {str(e)}", indent=4)
        return False

# --- Main Logic ---
def download_epapers():
    """Fetches edition list and downloads PDFs for today."""
    today_date_api_str = get_today_date_str(DATE_FORMAT_API)
    today_date_folder_str = get_today_date_str(DATE_FORMAT_FOLDER)
    output_base_dir = f"NT_{today_date_folder_str}"

    # Enhanced startup logging
    logger.header("NAMASTE TELANGANA EPAPER DOWNLOADER")
    logger.info(f"Target Date: {today_date_api_str}")
    logger.info(f"Output Directory: {output_base_dir}")
    logger.info(f"Timestamp: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # Get Editions Hierarchy
    logger.section("FETCHING EDITION HIERARCHY")
    try:
        logger.info("Connecting to Namaste Telangana API...")
        # logger.detail("Endpoint", BASE_URL_HIERARCHY)

        response_hierarchy = requests.get(BASE_URL_HIERARCHY, timeout=30)
        response_hierarchy.raise_for_status()
        editions_data = response_hierarchy.json()

        total_groups = len(editions_data)
        total_editions = sum(len(group.get("editionlocation", [])) for group in editions_data)

        logger.success(f"Edition hierarchy fetched successfully")
        logger.detail("Edition Groups", total_groups)
        logger.detail("Total Editions", total_editions)

    except Exception as e:
        logger.error(f"Failed to fetch edition list: {e}")
        return

    os.makedirs(output_base_dir, exist_ok=True)
    logger.success(f"Created output directory: {output_base_dir}")
    
    # Process each edition group with progress tracking
    logger.section("PROCESSING EDITION GROUPS")

    # Create progress bar for edition groups
    group_progress = logger.progress_bar(len(editions_data), "Processing Groups")

    for edition_group in editions_data:
        org_location = edition_group.get("org_location")
        edition_locations = edition_group.get("editionlocation")

        if not org_location or not edition_locations:
            group_progress.update(1)
            continue

        folder_name = sanitize_filename(org_location)
        folder_path = os.path.join(output_base_dir, folder_name)
        os.makedirs(folder_path, exist_ok=True)

        logger.subsection(f"Group: {org_location}")
        logger.detail("Editions Count", len(edition_locations), indent=2)
        logger.detail("Output Folder", folder_name, indent=2)

        # Create progress bar for editions in this group
        edition_progress = tqdm(
            edition_locations,
            desc=f"  {Fore.CYAN}Processing {org_location}{Style.RESET_ALL}",
            leave=False,
            ncols=80,
            colour='cyan'
        )

        # Process each edition
        for edition in edition_progress:
            edition_name = edition.get("Editionlocation")
            edition_id = edition.get("EditionId")

            if not edition_name or not edition_id:
                continue

            edition_progress.set_description(f"  {Fore.CYAN}Processing: {edition_name[:20]}...{Style.RESET_ALL}")

            output_filename = f"{sanitize_filename(edition_name)}.pdf"
            output_filepath = os.path.join(folder_path, output_filename)

            if os.path.exists(output_filepath):
                logger.warning(f"Skipping {edition_name} - File already exists", indent=3)
                logger.update_stats('skipped')
                continue
            
            try:
                # Get all pages for this edition
                logger.info(f"Fetching pages for: {edition_name}", indent=3)
                page_data = {
                    "editionid": edition_id,
                    "editiondate": today_date_api_str,
                    "email": ""
                }

                response_pages = requests.post(BASE_URL_GET_PAGES, json=page_data, timeout=30)
                response_pages.raise_for_status()
                pages = response_pages.json()

                if not pages:
                    logger.error(f"No pages found for edition: {edition_name}", indent=3)
                    logger.update_stats('errors')
                    continue

                logger.success(f"Found {len(pages)} pages for {edition_name}", indent=3)
                logger.update_stats('total_pages', len(pages))

                # Download each page and save to temporary files first (like nt.py approach)
                temp_dir = os.path.join(folder_path, f"temp_{sanitize_filename(edition_name)}")
                os.makedirs(temp_dir, exist_ok=True)

                # Create progress bar for page downloads
                page_progress = tqdm(
                    total=len(pages),
                    desc=f"    {Fore.GREEN}Downloading pages{Style.RESET_ALL}",
                    leave=False,
                    ncols=70,
                    colour='green'
                )

                # Download pages sequentially to avoid duplicates and ensure proper ordering
                page_files = []
                downloaded_page_numbers = set()  # Track downloaded page numbers

                for page in pages:
                    page_id = page.get("PageId")
                    page_no = page.get("PageNo")

                    if not page_id or not page_no:
                        logger.warning(f"Skipping page with missing ID or number: ID={page_id}, No={page_no}", indent=4)
                        page_progress.update(1)
                        continue

                    # Validate and normalize page number
                    try:
                        page_num = int(page_no)
                        if page_num <= 0:
                            logger.warning(f"Invalid page number {page_no}, skipping", indent=4)
                            page_progress.update(1)
                            continue
                    except (ValueError, TypeError):
                        logger.warning(f"Non-numeric page number {page_no}, skipping", indent=4)
                        page_progress.update(1)
                        continue

                    # Check for duplicate page numbers
                    if page_num in downloaded_page_numbers:
                        logger.warning(f"Duplicate page number {page_num} detected, skipping duplicate", indent=4)
                        page_progress.update(1)
                        continue

                    # Download the page
                    pdf_content, pdf_filename = download_single_page_to_file(
                        page_id, edition_id, today_date_api_str, page_num
                    )

                    if pdf_content and pdf_filename:
                        # Save page to temporary file
                        temp_filename = f"page_{page_num:03d}_{pdf_filename}"
                        temp_filepath = os.path.join(temp_dir, temp_filename)

                        with open(temp_filepath, 'wb') as f:
                            f.write(pdf_content)

                        page_files.append({
                            'page_no': page_num,
                            'filepath': temp_filepath,
                            'filename': pdf_filename
                        })

                        downloaded_page_numbers.add(page_num)
                        logger.info(f"Downloaded page {page_num}: {temp_filename}", indent=5)
                    else:
                        logger.error(f"Failed to download page {page_num}", indent=4)

                    page_progress.update(1)
                    time.sleep(0.1)  # Small delay to avoid overwhelming server

                page_progress.close()

                # Sort pages by page number and merge them
                if page_files:
                    # Sort by page number
                    page_files.sort(key=lambda x: x['page_no'])
                    page_numbers = [pf['page_no'] for pf in page_files]
                    logger.info(f"Merging {len(page_files)} pages in order: {page_numbers}", indent=4)

                    merger = PdfMerger()
                    for page_file in page_files:
                        try:
                            if os.path.exists(page_file['filepath']):
                                merger.append(page_file['filepath'])
                            else:
                                logger.error(f"Page file not found: {page_file['filepath']}", indent=5)
                        except Exception as e:
                            logger.error(f"Failed to add page {page_file['page_no']} to merger: {str(e)}", indent=4)

                # Save the merged PDF
                if page_files:
                    merger.write(output_filepath)
                    merger.close()  # Ensure file is properly closed

                    file_size = os.path.getsize(output_filepath) / (1024 * 1024)  # MB
                    page_count = len(page_files)
                    expected_pages = len(pages)

                    # Validate the final PDF
                    pdf_valid = validate_pdf_pages(output_filepath, page_count)

                    if page_count == expected_pages and pdf_valid:
                        logger.success(f"✓ {edition_name} - {page_count}/{expected_pages} pages, {file_size:.1f}MB ✓", indent=3)
                    elif page_count == expected_pages:
                        logger.warning(f"⚠ {edition_name} - {page_count}/{expected_pages} pages, {file_size:.1f}MB (PDF validation failed)", indent=3)
                    else:
                        logger.warning(f"⚠ {edition_name} - {page_count}/{expected_pages} pages, {file_size:.1f}MB (some pages missing)", indent=3)

                    # Clean up temporary files
                    for page_file in page_files:
                        try:
                            if os.path.exists(page_file['filepath']):
                                os.remove(page_file['filepath'])
                        except Exception as e:
                            logger.warning(f"Failed to clean up temp file: {page_file['filepath']}", indent=4)

                    # Remove temporary directory
                    try:
                        if os.path.exists(temp_dir):
                            os.rmdir(temp_dir)
                    except Exception as e:
                        logger.warning(f"Failed to remove temp directory: {temp_dir}", indent=4)

                    logger.update_stats('downloads')
                else:
                    logger.error(f"Failed to download any pages for {edition_name}", indent=3)
                    logger.update_stats('errors')

                    # Clean up empty temp directory
                    try:
                        if os.path.exists(temp_dir):
                            os.rmdir(temp_dir)
                    except:
                        pass
                
            except Exception as e:
                logger.error(f"Error processing {edition_name}: {str(e)}", indent=3)
                logger.update_stats('errors')
                # Clean up partially downloaded file
                if os.path.exists(output_filepath):
                    try:
                        os.remove(output_filepath)
                        logger.warning(f"Cleaned up partial file: {output_filename}", indent=4)
                    except:
                        pass

        edition_progress.close()
        group_progress.update(1)

    group_progress.close()

    # Print final summary
    logger.print_summary()

def download_single_page_to_file(page_id, edition_id, date_str, page_no):
    """Download a single page PDF and return its content and filename (like nt.py approach)"""
    try:
        params = {
            'id': page_id,
            'type': '1',
            'EditionId': edition_id,
            'Date': date_str
        }

        response_info = requests.get(BASE_URL_DOWNLOAD_INFO, params=params, timeout=30)
        response_info.raise_for_status()
        download_info = response_info.json()

        if download_info.get("status") and download_info.get("FileName"):
            pdf_filename = download_info["FileName"]
            pdf_download_url = f"https://epaper.ntnews.com/Home/Download?Filename={pdf_filename}"

            response_pdf = requests.get(pdf_download_url, timeout=30)
            response_pdf.raise_for_status()

            # Validate PDF content
            if len(response_pdf.content) < 1000:  # PDF should be at least 1KB
                logger.warning(f"Page {page_no} seems too small ({len(response_pdf.content)} bytes)", indent=5)
                return None, None

            return response_pdf.content, pdf_filename
        else:
            logger.warning(f"Page {page_no}: Invalid download info - status: {download_info.get('status')}, filename: {download_info.get('FileName')}", indent=5)

    except Exception as e:
        logger.error(f"Failed to download page {page_no} (ID: {page_id}): {str(e)}", indent=4)

    return None, None

def authUser():
    """Enhanced user authentication with better logging"""
    global name

    logger.header("USER AUTHENTICATION")

    try:
        name = input(f"{Fore.CYAN}Enter User Name: {Style.RESET_ALL}")
        users = ["datta", "karthik1"]  # "santhi","dwaraka",

        logger.info(f"Authenticating user: {name}")

        if name not in users:
            logger.error(f"Access denied for user: {name}")
            logger.warning("User not in authorized list")
            input(f"{Fore.RED}Press Enter to exit...{Style.RESET_ALL}")
            exit()

        logger.success(f"User '{name}' found in authorized list")

        # Device verification
        logger.info("Verifying device registration...")
        link = f"https://temp-21dd4-default-rtdb.firebaseio.com/clients/uuid/{name}/.json"

        try:
            res = requests.get(link, timeout=10).text[1:-1]
            uuid = subprocess.check_output('wmic csproduct get UUID').decode("utf-8").split("\n")[1].strip()

            logger.detail("Device UUID", uuid[:8] + "..." + uuid[-8:])  # Partial UUID for security

            if res != uuid:
                logger.warning("Device not registered - sending registration request")

                method = "sendMessage"
                token = "1271352791:AAF2DSjJcLsE1BJpoh9CVuzKKlnaWSqfBss"
                requests.post(
                    url=f'https://api.telegram.org/bot{token}/{method}',
                    data={'chat_id': 512930889, 'text': f"{name} : {uuid} new device registration"}
                )

                logger.error("Device registration required")
                logger.info("Registration request sent to administrator")
                input(f"{Fore.YELLOW}Press Enter to exit...{Style.RESET_ALL}")
                exit()

            logger.success("Device verification successful")
            return True

        except Exception as e:
            logger.error(f"Device verification failed: {str(e)}")
            input(f"{Fore.RED}Press Enter to exit...{Style.RESET_ALL}")
            exit()

    except KeyboardInterrupt:
        logger.warning("Authentication cancelled by user")
        exit()
    except Exception as e:
        logger.error(f"Authentication error: {str(e)}")
        exit()

if __name__ == "__main__":
    try:
        # Clear screen for better presentation
        os.system('cls' if os.name == 'nt' else 'clear')

        # Run authentication
        authUser()

        # Start download process
        start_time = time.time()
        download_epapers()

        # Show completion time
        end_time = time.time()
        duration = end_time - start_time
        logger.info(f"Total execution time: {duration:.2f} seconds")

        # Keep window open
        input(f"\n{Fore.GREEN}Download process completed. Press Enter to exit...{Style.RESET_ALL}")

    except KeyboardInterrupt:
        logger.warning("Process interrupted by user")
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
    finally:
        # Cleanup any open progress bars
        if hasattr(logger, 'current_progress') and logger.current_progress:
            logger.current_progress.close()
