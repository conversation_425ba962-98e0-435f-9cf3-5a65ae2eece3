import io
import requests
import datetime
from PyPDF2 import  Pd<PERSON><PERSON><PERSON><PERSON>, PdfWriter
import concurrent.futures
import os
import subprocess


session=requests.session()
today = datetime.date.today().strftime("%Y-%m-%d")

def getPdfListById(id):
    try:
        url = f"https://epaper.prabhatkhabar.com/api/published-editions/slug/{id}/{today}"
        response = session.get(url)
        response.raise_for_status()  # Raises an error for bad responses
        data = response.json()
        return [f'https://cdnimg.prabhatkhabar.com/pdf/{page["currentImage"]["pdf"]}' for page in data["data"]["pages"]]
    except requests.RequestException as e:
        print(f"An error occurred: {e}")
        return []
    except KeyError as e:
        print(f"Missing key in response data: {e}")
        return []

def mergePdfs(pdfUrls, output_filename):
    pdf_writer = PdfWriter()
    
    def download_and_add_page(url):
        try:
            response = session.get(url)
            response.raise_for_status()
            pdf_data = response.content
            pdf_reader = PdfReader(io.BytesIO(pdf_data))
            return pdf_reader.pages
        except requests.RequestException as e:
            print(f"An error occurred while downloading {url}: {e}")
            return []
        except Exception as e:
            print(f"An error occurred while adding {url}: {e}")
            return []

    with concurrent.futures.ThreadPoolExecutor() as executor:
        pdf_pages = executor.map(download_and_add_page, pdfUrls)

    for pages in pdf_pages:
        for page in pages:
            pdf_writer.add_page(page)

    with open(output_filename, 'wb') as fh:
        pdf_writer.write(fh)


def getAllFolderAndPaperIds():
    try:
        url = "https://epaperadmin.prabhatkhabar.com/api/editions/grouped"
        response = session.get(url)
        response.raise_for_status()  # Raises an error for bad responses
        data = response.json()

        return {
            i["groupName"]: {j["name"]: j["slug"] for j in i["editions"]}
            for i in data.get("data", [])
        }
    except requests.RequestException as e:
        print(f"An error occurred: {e}")
        return {}
    except KeyError as e:
        print(f"Missing key in response data: {e}")
        return {}


def authUser():
	global name
	name=input("Enter User Name :")
	users=["datta","santhi","dwaraka"]
	if name not in users:
		print(name,"have no access for this program")
		input()
		exit()
	link="https://temp-21dd4-default-rtdb.firebaseio.com/clients/uuid/"+name+"/.json"
	res=requests.get(link).text[1:-1]
	uuid=subprocess.check_output('wmic csproduct get UUID').decode("utf-8").split("\n")[1].strip()
	if res!=uuid:
		method = "sendMessage"
		token = "1271352791:AAF2DSjJcLsE1BJpoh9CVuzKKlnaWSqfBss"
		requests.post(
		    url='https://api.telegram.org/bot{0}/{1}'.format(token, method),
		    data={'chat_id': 512930889, 'text': name+" : "+uuid+" new device registration"}
		)
		print("User doesn't register for this device, Althrough this device registration request will be sent")
		input()
		exit()
	return True

authUser()



output_folder = f"prabhatKhabar_{today}"
if not os.path.exists(output_folder):
    os.makedirs(output_folder)

groups = getAllFolderAndPaperIds()
for group_name, group in groups.items():
    group_folder = os.path.join(output_folder, group_name)
    if not os.path.exists(group_folder):
        os.makedirs(group_folder)
    with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
        futures = [
            executor.submit(
                lambda slug_name, slug: (
                    slug_name,
                    getPdfListById(slug),
                ),
                slug_name,
                slug,
            )
            for slug_name, slug in group.items()
        ]
        for future in concurrent.futures.as_completed(futures):
            slug_name, pdfUrls = future.result()
            output_file = os.path.join(group_folder, f"{slug_name}.pdf")
            if os.path.exists(output_file):
                print(f"Skipping {slug_name} as {output_file} already exists")
            else:
                print(f"Downloading {slug_name}")
                mergePdfs(pdfUrls, output_file)
#mergePdfs(getPdfListById("hazaribagh"), "prabhatKhabar.pdf")
