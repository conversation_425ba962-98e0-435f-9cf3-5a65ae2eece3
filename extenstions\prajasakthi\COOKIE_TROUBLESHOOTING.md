# Cookie Extraction Troubleshooting Guide

## 🚨 Issue: Chrome API Finding 0 Cookies

If you're seeing logs like:
```
getCookiesForSite: Found 0 cookies for https://epaper.prajasakti.com/
getCookiesForSite: Found 0 cookies for https://prajasakti.com/
```

This is a common issue with Chrome extensions and HttpOnly cookies.

## 🔧 Solutions (In Order of Preference)

### Solution 1: Content Script Injection (Automatic)
The extension now automatically tries this if the Chrome API fails:
- It injects a script into the current tab
- Gets `document.cookie` directly from the page
- This bypasses HttpOnly restrictions for accessible cookies

### Solution 2: Manual Cookie Input (Recommended)
1. **Click "Show Manual Cookie Input"** in the extension
2. **Get cookies from DevTools:**
   - Go to `epaper.prajasakti.com`
   - Press `F12` to open DevTools
   - Go to **Network** tab
   - Refresh the page
   - Click on any request to the site
   - Find the **Request Headers** section
   - Copy the entire **Cookie** header value
3. **Paste into the textarea** in the extension
4. **Click "Use Manual Cookies"**

### Solution 3: Copy from Browser DevTools
Alternative method to get cookies:
1. Go to `epaper.prajasakti.com`
2. Press `F12` → **Application** tab
3. In the left sidebar: **Storage** → **Cookies** → `epaper.prajasakti.com`
4. Select all cookies and copy them
5. Format as: `name1=value1; name2=value2; name3=value3`

## 📋 Cookie Format Example

Your cookies should look like this:
```
_ga=GA1.1.204981852.1749914034; _pk_id.24.86b6=c84461cc970e4310.1749914035.; csrftoken=3MUQjBbnuRB8vmMJVczRWjn93hz3WANdSCCYf5lgl32NUkf4mh1ile2Ft273dvVF; cf_clearance=VLmGAMVbh7LPxq7SmEonkqsTSmFs4ikUmVjxGo2mgtQ-1749927356-1.2.1.1-KpPGBwTlbn35rHJctdZgsFrM5KTCO2QjiVLHK8ENr54bpEmC2LtDd_Jb4kuDd6PjW.OUst64W7OQAsdz2D8v0zWDO6h2UfFKMr1P59BDzrp1Oqz0phWCTyS7u1.SZiW8xhFus6WwoDKjrPoe.59bwnvFq20OjgNNHY_2OrGvuMBy8bveJ.Lzdsz3GSyvmMK1uenZaSwg1GT0WVjnJji8IMwsFFsuHAke9QMRbeyfCnh377hP00zKOp9rDVwBkpCH_sVnd_WmZndpFjnoawQPqWCQEqZh2QTUcUOrmiAq1ugFsezm93VqsOLEqM22Rd6Kw5mCtMoUHylx_t0Jz45uo7dMRa_PAlh1WG24KgWrMYOfmwa534uKlXk1U1eXPLcS
```

## 🔍 Important Cookies for Prajasakti

Make sure these cookies are included:
- `csrftoken` - Required for authentication
- `cf_clearance` - Cloudflare clearance
- `_pk_*` - Analytics cookies (may be required)
- `_ga*` - Google Analytics (may be required)

## 🐛 Debugging Steps

1. **Check Debug Panel** in the extension:
   - Auto Cookies: Shows if automatic extraction worked
   - Manual Cookies: Shows if manual input has cookies
   - Cookie Preview: Shows first 200 characters

2. **Check Browser Console** on epaper.prajasakti.com:
   ```javascript
   // Run this in the console to see available cookies
   console.log('Document cookies:', document.cookie);
   ```

3. **Check Extension Logs**:
   - Background script: chrome://extensions/ → "service worker"
   - Popup: Right-click extension → "Inspect popup"

## ⚡ Quick Test

To verify cookies work:
1. Get cookies using any method above
2. Test with a simple fetch:
   ```javascript
   fetch('https://epaper.prajasakti.com/view/?date=2025-06-15&edition=3&pg_no=1', {
     headers: { 'Cookie': 'YOUR_COOKIE_STRING_HERE' }
   }).then(r => console.log('Status:', r.status))
   ```

## 🎯 Why Chrome API Might Fail

- **HttpOnly cookies**: Can't be accessed by extensions
- **SameSite restrictions**: Cookies with strict SameSite policies
- **Secure cookies**: May require HTTPS context
- **Domain mismatches**: Cookies set for different subdomains
- **Extension permissions**: Missing host permissions

## 🚀 Next Steps

Once you have working cookies:
1. The extension will remember them during the session
2. You can download multiple editions
3. Cookies will work until they expire (usually 24 hours)
4. When they expire, just repeat the manual process

The manual method is actually more reliable than automatic extraction for many sites!
